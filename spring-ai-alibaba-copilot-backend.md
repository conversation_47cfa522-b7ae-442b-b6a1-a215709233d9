# Spring AI Alibaba Copilot 后端项目详解

## 项目概述

Spring AI Alibaba Copilot是一个基于Spring AI和Vue3的智能编码助手，支持任务规划、分步执行和实时反馈。后端采用Spring Boot和Spring AI构建，集成了MCP(Model Context Protocol)工具，提供了强大的AI编码辅助能力。

## 技术栈

- **框架**: Spring Boot 3.4.5
- **AI集成**: Spring AI 1.0.0
- **Java版本**: Java 17
- **通信机制**: RESTful API + Server-Sent Events (SSE)
- **AI模型**: 通过Spring AI支持多种LLM模型（默认配置为阿里云DashScope的Qwen-Plus模型）

## 项目结构

```
src/main/
├── java/
│   └── com/
│       └── alibaba/
│           └── cloud/
│               └── ai/
│                   └── example/
│                       └── copilot/
│                           ├── CopilotApplication.java (应用入口)
│                           ├── controller/
│                           │   └── TaskController.java (任务控制器)
│                           ├── service/
│                           │   ├── LlmService.java (LLM服务)
│                           │   └── SseService.java (SSE服务)
│                           ├── planning/
│                           │   ├── TaskCoordinator.java (任务协调器)
│                           │   ├── TaskPlan.java (任务计划模型)
│                           │   ├── TaskPlanningPromptBuilder.java (提示词构建器)
│                           │   ├── TaskPlanningService.java (任务规划服务)
│                           │   └── TaskStep.java (任务步骤模型)
│                           └── template/
└── resources/
    ├── application.properties (应用配置)
    └── mcp-servers-config.json (MCP工具配置)
```

## 核心组件详解

### 1. CopilotApplication.java

应用程序入口点，使用标准的Spring Boot启动方式。

```java
@SpringBootApplication
public class CopilotApplication {
    private static final Logger logger = LoggerFactory.getLogger(CopilotApplication.class);

    public static void main(String[] args) {
        // 启动Spring Boot应用
        SpringApplication.run(CopilotApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  AI Copilot启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
```

### 2. TaskController.java

提供任务管理的REST API，包括创建任务、获取任务状态、取消任务等功能，以及SSE连接端点。

主要API端点：
- `GET /api/task/health`: 健康检查
- `POST /api/task/create`: 创建并开始执行任务
- `GET /api/task/status/{taskId}`: 获取任务状态
- `POST /api/task/cancel/{taskId}`: 取消任务
- `GET /api/task/stream/{taskId}`: 创建SSE连接以接收任务状态更新
- `POST /api/task/next-step/{taskId}`: 手动触发下一步规划
- `POST /api/task/retry/{taskId}/{stepIndex}`: 重试失败的步骤
- `GET /api/task/active`: 获取所有活跃任务

### 3. LlmService.java

提供对大语言模型的访问，封装了Spring AI的ChatClient，集成了MCP工具回调。

```java
@Service
public class LlmService {
    @Autowired
    private ChatClient.Builder chatClientBuilder;

    @Autowired
    private List<io.modelcontextprotocol.client.McpSyncClient> mcpSyncClients;

    private ChatClient chatClient;

    @PostConstruct
    public void initChatClient() {
        chatClient = chatClientBuilder
                .defaultToolCallbacks(new SyncMcpToolCallbackProvider(mcpSyncClients))
                .build();
    }

    public ChatClient getChatClient() {
        return chatClient;
    }
}
```

### 4. SseService.java

提供Server-Sent Events功能，用于向前端推送任务状态更新和执行进度。

主要功能：
- 创建和管理SSE连接
- 发送步骤流式内容更新
- 发送任务状态更新

### 5. 任务规划系统

#### TaskPlan.java

任务计划模型类，存储任务的标题、描述、步骤列表等信息。

主要属性：
- `taskId`: 任务ID
- `title`: 任务标题
- `description`: 任务描述
- `step`: 任务步骤
- `planStatus`: 全局计划状态
- `extraParams`: 上下文信息
- `isCompleted`: 任务完成标识

#### TaskStep.java

任务步骤模型类，存储单个任务步骤的信息。

主要属性：
- `stepIndex`: 步骤索引
- `stepRequirement`: 执行要求/执行内容详情
- `toolName`: 执行需要调用的方法
- `result`: 执行需要返回的内容
- `status`: 执行状态
- `startTime`: 步骤开始执行的时间戳
- `endTime`: 步骤结束执行的时间戳

#### TaskCoordinator.java

任务协调器，负责协调任务的执行流程，是系统的核心组件。

主要职责：
- 启动任务执行
- 管理任务状态
- 协调步骤执行
- 处理执行结果

#### TaskPlanningService.java

任务规划服务，负责生成任务计划和步骤。

主要功能：
- 根据用户请求生成初始任务计划
- 根据执行结果动态规划下一步

#### TaskPlanningPromptBuilder.java

提示词构建器，用于构建发送给LLM的提示词。

主要功能：
- 构建任务规划提示词
- 构建步骤执行提示词

## 配置文件

### application.properties

应用程序主配置文件，包含Spring Boot配置、AI模型配置、MCP配置等。

```properties
debug=true
spring.application.name=mcp
spring.main.web-application-type=servlet

spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.enabled=true

# AI模型配置
spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode
spring.ai.openai.api-key=${DASHSCOPE_API_KEY:sk-311a368820f04fea86a9f163290cbd09}
spring.ai.openai.chat.options.model=qwen-plus

# MCP配置
spring.ai.mcp.client.stdio.servers-configuration=classpath:/mcp-servers-config.json
spring.ai.mcp.client.timeout=60000
spring.ai.mcp.client.stdio.timeout=60000

# 日志配置
logging.level.root=INFO
logging.level.org.springframework.ai.mcp=INFO
logging.level.org.springframework.ai.mcp.client.transport.StdioClientTransport=INFO
logging.level.org.springframework.ai.mcp.samples.brave=INFO
logging.level.io.modelcontextprotocol.client=INFO
logging.level.io.modelcontextprotocol.spec=INFO
```

### mcp-servers-config.json

MCP工具配置文件，定义了可用的MCP服务器。

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "F:\\application\\node\\npx.cmd",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "."
      ]
    }
  }
}
```

## 工作流程

1. 用户通过前端提交编码需求
2. TaskController接收请求并创建任务
3. TaskCoordinator启动任务执行
4. TaskPlanningService生成初始任务计划
5. TaskCoordinator执行第一个步骤
6. 根据执行结果，TaskPlanningService规划下一步
7. 重复步骤5-6直到任务完成
8. 整个过程中，SseService向前端推送实时更新

## 依赖管理

项目使用Maven管理依赖，主要依赖包括：

- Spring Boot Starter
- Spring Boot Web Starter
- Spring Boot WebSocket Starter
- Spring AI Starter Model OpenAI
- Spring AI Starter MCP Client
- Jackson for JSON processing

## 扩展点

1. **支持更多AI模型**: 通过配置Spring AI可以轻松切换不同的AI模型提供商
2. **增加更多MCP工具**: 可以在mcp-servers-config.json中添加更多工具，如Git操作、数据库操作等
3. **任务模板系统**: 可以扩展模板系统，支持更多类型的项目模板
4. **用户认证与权限**: 可以添加Spring Security进行用户认证和权限管理 