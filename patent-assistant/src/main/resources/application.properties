# åºç¨åºæ¬éç½®
spring.application.name=patent-assistant
server.port=8080
debug=false

# æ°æ®åºéç½®
spring.datasource.url=jdbc:h2:file:./data/patentdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Webèµæºéç½®
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.enabled=true

# AIæ¨¡åéç½®
spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode
spring.ai.openai.api-key=${DASHSCOPE_API_KEY:your-api-key-here}
spring.ai.openai.chat.options.model=qwen-plus

# MCPéç½®
spring.ai.mcp.client.stdio.servers-configuration=classpath:/mcp-servers-config.json
spring.ai.mcp.client.timeout=60000
spring.ai.mcp.client.stdio.timeout=60000

# ä¸å©å©æç¹å®éç½®
patent.assistant.template-path=classpath:/templates/patent
patent.assistant.max-search-results=50
patent.assistant.default-language=zh_CN

# æ¥å¿éç½®
logging.level.root=INFO
logging.level.com.patentai=DEBUG
logging.level.org.springframework.ai=INFO
logging.level.org.springframework.web=INFO 