package com.patentai.assistant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 专利助手系统主应用类
 * 提供AI驱动的专利撰写、检索和分析功能
 */
@SpringBootApplication
public class PatentAssistantApplication {

    private static final Logger logger = LoggerFactory.getLogger(PatentAssistantApplication.class);

    public static void main(String[] args) {
        // 启动Spring Boot应用
        SpringApplication.run(PatentAssistantApplication.class, args);
        logger.info("(♥◠‿◠)ﾉﾞ  专利助手系统启动成功   ლ(´ڡ`ლ)ﾞ");
    }
} 