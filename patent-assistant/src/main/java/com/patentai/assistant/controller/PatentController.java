package com.patentai.assistant.controller;

import com.patentai.assistant.model.Patent;
import com.patentai.assistant.planning.PatentTaskCoordinator;
import com.patentai.assistant.planning.PatentTaskPlan;
import com.patentai.assistant.service.PatentService;
import com.patentai.assistant.service.SseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 专利控制器
 * 提供专利相关的API接口
 */
@RestController
@RequestMapping("/api/patents")
public class PatentController {

    private static final Logger logger = LoggerFactory.getLogger(PatentController.class);

    @Autowired
    private PatentService patentService;

    @Autowired
    private PatentTaskCoordinator taskCoordinator;

    @Autowired
    private SseService sseService;

    /**
     * 健康检查端点
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "ok");
        response.put("message", "专利助手服务正常运行");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 获取所有专利
     * @return 专利列表
     */
    @GetMapping
    public ResponseEntity<List<Patent>> getAllPatents() {
        List<Patent> patents = patentService.getAllPatents();
        return ResponseEntity.ok(patents);
    }

    /**
     * 获取单个专利
     * @param id 专利ID
     * @return 专利详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Patent> getPatent(@PathVariable Long id) {
        return patentService.getPatent(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 创建专利
     * @param patent 专利信息
     * @return 创建的专利
     */
    @PostMapping
    public ResponseEntity<Patent> createPatent(@RequestBody Patent patent) {
        Patent createdPatent = patentService.createPatent(patent);
        return ResponseEntity.ok(createdPatent);
    }

    /**
     * 更新专利
     * @param id 专利ID
     * @param patent 专利信息
     * @return 更新后的专利
     */
    @PutMapping("/{id}")
    public ResponseEntity<Patent> updatePatent(@PathVariable Long id, @RequestBody Patent patent) {
        return patentService.updatePatent(id, patent)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 删除专利
     * @param id 专利ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deletePatent(@PathVariable Long id) {
        boolean deleted = patentService.deletePatent(id);
        
        Map<String, Object> response = new HashMap<>();
        if (deleted) {
            response.put("status", "success");
            response.put("message", "专利已删除");
            return ResponseEntity.ok(response);
        } else {
            response.put("status", "error");
            response.put("message", "专利不存在或删除失败");
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 创建并开始执行专利撰写任务
     * @param request 任务请求
     * @return 任务ID和初始状态
     */
    @PostMapping("/tasks/create")
    public ResponseEntity<Map<String, Object>> createTask(@RequestBody Map<String, String> request) {
        try {
            String userRequest = request.get("query");
            if (userRequest == null || userRequest.trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", "请求内容不能为空"
                ));
            }

            // 生成任务ID
            String taskId = UUID.randomUUID().toString();

            logger.info("创建新专利撰写任务，任务ID: {}, 请求: {}", taskId, userRequest);

            // 执行任务
            taskCoordinator.startTask(userRequest, taskId);

            // 返回任务创建成功的响应
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("status", "processing");
            response.put("message", "任务已创建并开始执行，请通过SSE连接获取实时进度");
            response.put("timestamp", System.currentTimeMillis());

            logger.info("任务创建成功，任务ID: {}, 已启动异步执行", taskId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("创建任务失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "创建任务失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取任务状态
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/tasks/status/{taskId}")
    public ResponseEntity<Map<String, Object>> getTaskStatus(@PathVariable String taskId) {
        try {
            // 获取真实的任务信息
            PatentTaskPlan taskPlan = taskCoordinator.getTaskStatus(taskId);

            // 如果任务不存在，返回错误信息
            if (taskPlan == null) {
                return ResponseEntity.status(404).body(Map.of(
                    "status", "error",
                    "message", "任务不存在: " + taskId
                ));
            }

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("status", taskPlan.getPlanStatus());
            response.put("title", taskPlan.getTitle());
            response.put("description", taskPlan.getDescription());
            response.put("steps", taskPlan.getSteps());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取任务状态失败，任务ID: {}", taskId, e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "获取任务状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 取消结果
     */
    @PostMapping("/tasks/cancel/{taskId}")
    public ResponseEntity<Map<String, Object>> cancelTask(@PathVariable String taskId) {
        try {
            boolean cancelled = taskCoordinator.cancelTask(taskId);
            
            if (cancelled) {
                return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "任务已取消"
                ));
            } else {
                return ResponseEntity.status(404).body(Map.of(
                    "status", "error",
                    "message", "任务不存在: " + taskId
                ));
            }

        } catch (Exception e) {
            logger.error("取消任务失败，任务ID: {}", taskId, e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "取消任务失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 创建SSE连接以接收任务状态更新
     * @param taskId 任务ID
     * @param clientId 客户端ID
     * @return SSE连接
     */
    @GetMapping("/tasks/stream/{taskId}")
    public SseEmitter streamTaskUpdates(@PathVariable String taskId,
                                       @RequestParam(defaultValue = "default") String clientId) {
        logger.info("创建SSE连接，任务ID: {}, 客户端ID: {}", taskId, clientId);

        // 使用SseService创建连接
        SseEmitter emitter = sseService.createConnection(taskId, clientId);

        // 发送初始连接确认消息
        try {
            emitter.send(SseEmitter.event()
                .name("connected")
                .data("{\"message\":\"SSE连接已建立\",\"taskId\":\"" + taskId + "\",\"timestamp\":" + System.currentTimeMillis() + "}"));
        } catch (Exception e) {
            logger.error("发送SSE初始消息失败，任务ID: {}, 客户端ID: {}", taskId, clientId, e);
        }

        return emitter;
    }
} 