package com.patentai.assistant.planning;

import com.patentai.assistant.service.LlmService;
import com.patentai.assistant.service.SseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 专利任务协调器
 * 负责协调专利撰写任务的执行流程
 */
@Service
public class PatentTaskCoordinator {

    private static final Logger logger = LoggerFactory.getLogger(PatentTaskCoordinator.class);

    @Autowired
    private LlmService llmService;

    @Autowired
    private PatentTaskPlanningService planningService;

    @Autowired
    private SseService sseService;

    // 存储活跃任务
    private final Map<String, PatentTaskPlan> activeTasks = new ConcurrentHashMap<>();

    // 线程池执行任务
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 启动专利撰写任务
     * @param userRequest 用户请求
     * @param taskId 任务ID
     */
    public void startTask(String userRequest, String taskId) {
        logger.info("开始执行专利撰写任务，任务ID: {}", taskId);

        // 异步执行任务
        executorService.submit(() -> {
            try {
                // 1. 生成初始任务计划
                PatentTaskPlan taskPlan = planningService.generateInitialPlan(userRequest, taskId);
                activeTasks.put(taskId, taskPlan);

                // 2. 通知前端任务已创建
                sseService.sendTaskUpdate(taskId, taskPlan);

                // 3. 执行第一个步骤
                executeNextStep(taskId);

            } catch (Exception e) {
                logger.error("任务执行失败，任务ID: {}", taskId, e);
                updateTaskStatus(taskId, "FAILED");
            }
        });
    }

    /**
     * 执行下一个步骤
     * @param taskId 任务ID
     */
    public void executeNextStep(String taskId) {
        PatentTaskPlan taskPlan = activeTasks.get(taskId);
        if (taskPlan == null) {
            logger.error("找不到任务，任务ID: {}", taskId);
            return;
        }

        // 查找下一个待执行的步骤
        PatentTaskStep nextStep = findNextStep(taskPlan);
        if (nextStep == null) {
            logger.info("任务所有步骤已完成，任务ID: {}", taskId);
            updateTaskStatus(taskId, "COMPLETED");
            return;
        }

        // 执行步骤
        executeStep(taskId, nextStep);
    }

    /**
     * 执行指定步骤
     * @param taskId 任务ID
     * @param step 步骤
     */
    private void executeStep(String taskId, PatentTaskStep step) {
        logger.info("执行任务步骤，任务ID: {}, 步骤: {}", taskId, step.getStepIndex());

        // 更新步骤状态
        step.setStatus("EXECUTING");
        step.setStartTime(System.currentTimeMillis());
        updateTaskPlan(taskId);

        // 异步执行步骤
        executorService.submit(() -> {
            try {
                // 根据步骤类型调用不同的处理方法
                String result = planningService.executeStep(taskId, step);
                
                // 更新步骤状态和结果
                step.setResult(result);
                step.setStatus("COMPLETED");
                step.setEndTime(System.currentTimeMillis());
                updateTaskPlan(taskId);

                // 规划下一步
                planNextStep(taskId, step);

            } catch (Exception e) {
                logger.error("步骤执行失败，任务ID: {}, 步骤: {}", taskId, step.getStepIndex(), e);
                step.setStatus("FAILED");
                step.setResult("执行失败: " + e.getMessage());
                step.setEndTime(System.currentTimeMillis());
                updateTaskPlan(taskId);
            }
        });
    }

    /**
     * 规划下一步
     * @param taskId 任务ID
     * @param currentStep 当前步骤
     */
    private void planNextStep(String taskId, PatentTaskStep currentStep) {
        PatentTaskPlan taskPlan = activeTasks.get(taskId);
        if (taskPlan == null) {
            return;
        }

        // 根据当前步骤结果规划下一步
        planningService.planNextStep(taskId, currentStep);
        
        // 执行下一步
        executeNextStep(taskId);
    }

    /**
     * 查找下一个待执行的步骤
     * @param taskPlan 任务计划
     * @return 下一个待执行的步骤，如果没有则返回null
     */
    private PatentTaskStep findNextStep(PatentTaskPlan taskPlan) {
        return taskPlan.getSteps().stream()
                .filter(step -> "PENDING".equals(step.getStatus()))
                .min((s1, s2) -> Integer.compare(s1.getPriority(), s2.getPriority()))
                .orElse(null);
    }

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 状态
     */
    private void updateTaskStatus(String taskId, String status) {
        PatentTaskPlan taskPlan = activeTasks.get(taskId);
        if (taskPlan != null) {
            taskPlan.setPlanStatus(status);
            if ("COMPLETED".equals(status)) {
                taskPlan.setIsCompleted(true);
            }
            taskPlan.setUpdatedAt(System.currentTimeMillis());
            sseService.sendTaskUpdate(taskId, taskPlan);
        }
    }

    /**
     * 更新任务计划
     * @param taskId 任务ID
     */
    private void updateTaskPlan(String taskId) {
        PatentTaskPlan taskPlan = activeTasks.get(taskId);
        if (taskPlan != null) {
            taskPlan.setUpdatedAt(System.currentTimeMillis());
            sseService.sendTaskUpdate(taskId, taskPlan);
        }
    }

    /**
     * 获取任务状态
     * @param taskId 任务ID
     * @return 任务计划
     */
    public PatentTaskPlan getTaskStatus(String taskId) {
        return activeTasks.get(taskId);
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTask(String taskId) {
        PatentTaskPlan taskPlan = activeTasks.get(taskId);
        if (taskPlan != null) {
            taskPlan.setPlanStatus("CANCELLED");
            taskPlan.setUpdatedAt(System.currentTimeMillis());
            sseService.sendTaskUpdate(taskId, taskPlan);
            return true;
        }
        return false;
    }

    /**
     * 获取所有活跃任务
     * @return 活跃任务Map
     */
    public Map<String, PatentTaskPlan> getActiveTasks() {
        return activeTasks;
    }
} 