package com.patentai.assistant.planning;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 专利任务提示词构建器
 * 负责构建专利任务相关的提示词
 */
@Component
public class PatentTaskPromptBuilder {

    private static final Logger logger = LoggerFactory.getLogger(PatentTaskPromptBuilder.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 构建初始任务规划提示词
     * @param userRequest 用户请求
     * @return 提示词文本
     */
    public String buildInitialPlanningPrompt(String userRequest) {
        return """
                你是一个专业的专利撰写助手，负责帮助用户规划和撰写专利文档。
                你需要根据用户的发明描述，制定一个详细的专利撰写计划。
                
                请按照以下步骤进行规划：
                1. 分析用户的发明描述，理解其核心技术点和创新点
                2. 确定专利的技术领域和背景技术
                3. 规划专利撰写的步骤，包括但不限于：
                   - 发明信息收集和分析
                   - 相似专利检索
                   - 权利要求撰写
                   - 说明书撰写
                   - 专利文档优化
                
                你的输出应该是一个JSON格式的任务计划，包含以下字段：
                - title: 专利标题
                - description: 专利简要描述
                - steps: 步骤列表，每个步骤包含以下字段：
                  - stepIndex: 步骤索引（从1开始）
                  - title: 步骤标题
                  - stepRequirement: 步骤详细描述
                  - toolName: 需要使用的工具名称（可选）
                  - stepType: 步骤类型（ANALYSIS, DRAFTING, REVIEW, OPTIMIZATION）
                  - priority: 优先级（1-10，数字越小优先级越高）
                
                请确保你的规划是详细且可执行的，能够指导用户一步步完成专利撰写过程。
                """;
    }

    /**
     * 构建步骤执行提示词
     * @param step 步骤
     * @return 提示词文本
     */
    public String buildStepExecutionPrompt(PatentTaskStep step) {
        String basePrompt = """
                你是一个专业的专利撰写助手，现在需要执行专利撰写计划中的一个步骤。
                请根据步骤要求，提供专业、详细的输出。
                
                步骤类型: %s
                步骤标题: %s
                
                请确保你的输出符合专利文档的专业要求和格式规范。
                对于权利要求，要确保清晰、完整地定义保护范围。
                对于说明书部分，要确保充分支持权利要求，并详细描述技术实现。
                
                请直接提供结果，无需解释你的思考过程。
                """;
        
        return String.format(basePrompt, step.getStepType(), step.getTitle());
    }

    /**
     * 解析计划JSON
     * @param planJson 计划JSON字符串
     * @return 任务计划对象
     */
    public PatentTaskPlan parsePlanJson(String planJson) {
        try {
            PatentTaskPlan plan = new PatentTaskPlan();
            
            // 尝试直接解析完整的JSON
            try {
                plan = objectMapper.readValue(planJson, PatentTaskPlan.class);
                return plan;
            } catch (Exception e) {
                logger.warn("无法直接解析完整JSON，尝试解析部分字段: {}", e.getMessage());
            }
            
            // 如果直接解析失败，尝试解析部分字段
            Map<String, Object> jsonMap = objectMapper.readValue(planJson, Map.class);
            
            if (jsonMap.containsKey("title")) {
                plan.setTitle((String) jsonMap.get("title"));
            }
            
            if (jsonMap.containsKey("description")) {
                plan.setDescription((String) jsonMap.get("description"));
            }
            
            if (jsonMap.containsKey("steps") && jsonMap.get("steps") instanceof List) {
                List<Map<String, Object>> stepsJson = (List<Map<String, Object>>) jsonMap.get("steps");
                List<PatentTaskStep> steps = new ArrayList<>();
                
                for (Map<String, Object> stepJson : stepsJson) {
                    PatentTaskStep step = new PatentTaskStep();
                    
                    if (stepJson.containsKey("stepIndex")) {
                        step.setStepIndex(((Number) stepJson.get("stepIndex")).intValue());
                    }
                    
                    if (stepJson.containsKey("title")) {
                        step.setTitle((String) stepJson.get("title"));
                    }
                    
                    if (stepJson.containsKey("stepRequirement")) {
                        step.setStepRequirement((String) stepJson.get("stepRequirement"));
                    }
                    
                    if (stepJson.containsKey("toolName")) {
                        step.setToolName((String) stepJson.get("toolName"));
                    }
                    
                    if (stepJson.containsKey("stepType")) {
                        step.setStepType((String) stepJson.get("stepType"));
                    }
                    
                    if (stepJson.containsKey("priority")) {
                        step.setPriority(((Number) stepJson.get("priority")).intValue());
                    }
                    
                    steps.add(step);
                }
                
                plan.setSteps(steps);
            }
            
            return plan;
            
        } catch (Exception e) {
            logger.error("解析计划JSON失败", e);
            throw new RuntimeException("解析计划JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将任务计划转换为JSON
     * @param plan 任务计划
     * @return JSON字符串
     */
    public String planToJson(PatentTaskPlan plan) {
        try {
            return objectMapper.writeValueAsString(plan);
        } catch (JsonProcessingException e) {
            logger.error("转换计划为JSON失败", e);
            throw new RuntimeException("转换计划为JSON失败: " + e.getMessage(), e);
        }
    }
} 