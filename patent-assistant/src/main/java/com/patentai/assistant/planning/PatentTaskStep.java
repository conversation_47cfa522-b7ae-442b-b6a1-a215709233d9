package com.patentai.assistant.planning;

import lombok.Data;

/**
 * 专利任务步骤模型类
 * 存储单个专利任务步骤的索引和要求描述
 */
@Data
public class PatentTaskStep {
    /**
     * 步骤索引
     */
    private int stepIndex;

    /**
     * 步骤标题
     */
    private String title;

    /**
     * 步骤要求/内容详情
     */
    private String stepRequirement;

    /**
     * 需要调用的工具名称
     */
    private String toolName;

    /**
     * 步骤执行结果
     */
    private String result;

    /**
     * 执行状态
     * PENDING: 待执行
     * EXECUTING: 执行中
     * COMPLETED: 已完成
     * FAILED: 执行失败
     */
    private String status = "PENDING";

    /**
     * 步骤开始执行的时间戳
     */
    private long startTime;

    /**
     * 步骤结束执行的时间戳
     */
    private long endTime;

    /**
     * 步骤类型
     * ANALYSIS: 分析阶段
     * DRAFTING: 撰写阶段
     * REVIEW: 审查阶段
     * OPTIMIZATION: 优化阶段
     */
    private String stepType;

    /**
     * 步骤依赖的前置步骤索引
     */
    private int[] dependencies;

    /**
     * 步骤执行的优先级（1-10，数字越小优先级越高）
     */
    private int priority = 5;
} 