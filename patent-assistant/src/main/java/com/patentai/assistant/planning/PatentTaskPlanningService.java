package com.patentai.assistant.planning;

import com.patentai.assistant.service.LlmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 专利任务规划服务
 * 负责生成专利撰写任务计划和步骤
 */
@Service
public class PatentTaskPlanningService {

    private static final Logger logger = LoggerFactory.getLogger(PatentTaskPlanningService.class);

    @Autowired
    private LlmService llmService;

    @Autowired
    private PatentTaskPromptBuilder promptBuilder;

    /**
     * 生成初始任务计划
     * @param userRequest 用户请求
     * @param taskId 任务ID
     * @return 任务计划
     */
    public PatentTaskPlan generateInitialPlan(String userRequest, String taskId) {
        logger.info("生成初始任务计划，任务ID: {}", taskId);

        try {
            // 构建提示词
            String promptText = promptBuilder.buildInitialPlanningPrompt(userRequest);
            SystemMessage systemMessage = new SystemMessage(promptText);
            UserMessage userMessage = new UserMessage(userRequest);
            
            List<Message> messages = new ArrayList<>();
            messages.add(systemMessage);
            messages.add(userMessage);
            
            Prompt prompt = new Prompt(messages);
            
            // 调用LLM生成计划
            ChatResponse response = llmService.getChatClient().call(prompt);
            String planJson = response.getResult().getOutput().getContent();
            
            // 解析返回的JSON为任务计划对象
            PatentTaskPlan taskPlan = promptBuilder.parsePlanJson(planJson);
            taskPlan.setTaskId(taskId);
            taskPlan.setPlanStatus("PLANNING");
            
            logger.info("初始任务计划生成成功，任务ID: {}, 步骤数: {}", taskId, 
                    taskPlan.getSteps() != null ? taskPlan.getSteps().size() : 0);
            
            return taskPlan;
            
        } catch (Exception e) {
            logger.error("生成初始任务计划失败，任务ID: {}", taskId, e);
            throw new RuntimeException("生成初始任务计划失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行任务步骤
     * @param taskId 任务ID
     * @param step 步骤
     * @return 执行结果
     */
    public String executeStep(String taskId, PatentTaskStep step) {
        logger.info("执行任务步骤，任务ID: {}, 步骤: {}", taskId, step.getStepIndex());

        try {
            // 构建步骤执行提示词
            String promptText = promptBuilder.buildStepExecutionPrompt(step);
            SystemMessage systemMessage = new SystemMessage(promptText);
            UserMessage userMessage = new UserMessage(step.getStepRequirement());
            
            List<Message> messages = new ArrayList<>();
            messages.add(systemMessage);
            messages.add(userMessage);
            
            Prompt prompt = new Prompt(messages);
            
            // 调用LLM执行步骤
            ChatResponse response = llmService.getChatClient().call(prompt);
            String result = response.getResult().getOutput().getContent();
            
            logger.info("步骤执行成功，任务ID: {}, 步骤: {}", taskId, step.getStepIndex());
            return result;
            
        } catch (Exception e) {
            logger.error("执行任务步骤失败，任务ID: {}, 步骤: {}", taskId, step.getStepIndex(), e);
            throw new RuntimeException("执行任务步骤失败: " + e.getMessage(), e);
        }
    }

    /**
     * 规划下一步
     * @param taskId 任务ID
     * @param currentStep 当前步骤
     */
    public void planNextStep(String taskId, PatentTaskStep currentStep) {
        logger.info("规划下一步，任务ID: {}, 当前步骤: {}", taskId, currentStep.getStepIndex());
        
        // 在实际实现中，可以根据当前步骤的结果动态规划下一步
        // 这里暂时不做额外处理，使用初始计划中的步骤
    }
} 