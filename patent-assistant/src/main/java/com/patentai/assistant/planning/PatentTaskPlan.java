package com.patentai.assistant.planning;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 专利任务计划模型类
 * 存储专利撰写任务的标题、描述、步骤列表
 */
@Data
public class PatentTaskPlan {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务步骤列表
     */
    private List<PatentTaskStep> steps = new ArrayList<>();

    /**
     * 全局计划状态
     */
    private String planStatus;

    /**
     * 关联的专利ID
     */
    private Long patentId;

    /**
     * 上下文信息（JSON格式）
     */
    private String extraParams;

    /**
     * 任务完成标识
     */
    private Boolean isCompleted = false;

    /**
     * 创建时间戳
     */
    private long createdAt = System.currentTimeMillis();

    /**
     * 更新时间戳
     */
    private long updatedAt = System.currentTimeMillis();
} 