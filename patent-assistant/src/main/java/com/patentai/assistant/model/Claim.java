package com.patentai.assistant.model;

import jakarta.persistence.*;
import lombok.Data;

/**
 * 权利要求模型类
 * 存储专利权利要求的内容和关系
 */
@Entity
@Data
public class Claim {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 权利要求序号
     */
    private Integer claimNumber;

    /**
     * 权利要求类型
     */
    @Enumerated(EnumType.STRING)
    private ClaimType type;

    /**
     * 权利要求文本
     */
    @Column(length = 5000)
    private String text;

    /**
     * 所属专利
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patent_id")
    private Patent patent;

    /**
     * 引用的权利要求ID（从属权利要求引用的独立权利要求）
     */
    private Long referencedClaimId;

    /**
     * 权利要求类型枚举
     */
    public enum ClaimType {
        INDEPENDENT,     // 独立权利要求
        DEPENDENT        // 从属权利要求
    }
} 