package com.patentai.assistant.model;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 说明书模型类
 * 存储专利说明书的各部分内容
 */
@Entity
@Data
public class Specification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 关联的专利ID
     */
    @OneToOne
    @JoinColumn(name = "patent_id")
    private Patent patent;

    /**
     * 技术领域
     */
    @Column(length = 2000)
    private String technicalField;

    /**
     * 背景技术
     */
    @Column(length = 5000)
    private String background;

    /**
     * 发明内容
     */
    @Column(length = 10000)
    private String summary;

    /**
     * 附图说明
     */
    @Column(length = 2000)
    private String drawingDescription;

    /**
     * 具体实施方式
     */
    @Column(columnDefinition = "TEXT")
    private String detailedDescription;

    /**
     * 有益效果
     */
    @Column(length = 3000)
    private String advantages;

    /**
     * 版本号
     */
    private Integer version = 1;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 最后编辑人ID
     */
    private Long lastEditorId;
} 