package com.patentai.assistant.model;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 专利模型类
 * 存储专利的基本信息和内容
 */
@Entity
@Data
public class Patent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 专利标题
     */
    private String title;

    /**
     * 专利摘要
     */
    @Column(length = 2000)
    private String abstractText;

    /**
     * 技术领域
     */
    private String technicalField;

    /**
     * 背景技术
     */
    @Column(length = 5000)
    private String background;

    /**
     * 发明内容
     */
    @Column(length = 10000)
    private String summary;

    /**
     * 附图说明
     */
    @Column(length = 2000)
    private String drawingDescription;

    /**
     * 具体实施方式
     */
    @Column(columnDefinition = "TEXT")
    private String detailedDescription;

    /**
     * 专利状态
     */
    @Enumerated(EnumType.STRING)
    private PatentStatus status = PatentStatus.DRAFT;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 权利要求列表
     */
    @OneToMany(mappedBy = "patent", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Claim> claims = new ArrayList<>();

    /**
     * 专利状态枚举
     */
    public enum PatentStatus {
        DRAFT,          // 草稿
        IN_PROGRESS,    // 撰写中
        REVIEW,         // 审核中
        FINALIZED,      // 已定稿
        FILED,          // 已提交
        PUBLISHED,      // 已公开
        GRANTED,        // 已授权
        REJECTED,       // 已驳回
        ABANDONED       // 已放弃
    }
} 