# Spring AI Alibaba Copilot API调用示例

## 创建并开始执行任务

### 使用curl创建新任务

```bash
curl -X POST http://localhost:8080/api/task/create \
  -H "Content-Type: application/json" \
  -d '{
    "query": "创建一个简单的Spring Boot REST API，包含用户管理功能"
  }'
```

### 响应示例

```json
{
  "taskId": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "message": "任务已创建并开始执行，请通过SSE连接获取实时进度",
  "timestamp": 1716112345678
}
```

## 获取任务状态

```bash
curl -X GET http://localhost:8080/api/task/status/550e8400-e29b-41d4-a716-446655440000
```

## 建立SSE连接接收实时更新

```bash
curl -N -H "Accept: text/event-stream" \
  http://localhost:8080/api/task/stream/550e8400-e29b-41d4-a716-446655440000?clientId=test-client
```

## 取消任务

```bash
curl -X POST http://localhost:8080/api/task/cancel/550e8400-e29b-41d4-a716-446655440000
```

## 手动触发下一步

```bash
curl -X POST http://localhost:8080/api/task/next-step/550e8400-e29b-41d4-a716-446655440000 \
  -H "Content-Type: application/json" \
  -d '{
    "stepResult": "步骤执行成功，已创建项目结构"
  }'
```

## 重试失败的步骤

```bash
curl -X POST http://localhost:8080/api/task/retry/550e8400-e29b-41d4-a716-446655440000/2
```

## 获取所有活跃任务

```bash
curl -X GET http://localhost:8080/api/task/active
``` 