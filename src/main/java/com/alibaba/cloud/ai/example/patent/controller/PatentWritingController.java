package com.alibaba.cloud.ai.example.patent.controller;

import com.alibaba.cloud.ai.example.patent.model.PatentWritingPlan;
import com.alibaba.cloud.ai.example.patent.model.PatentWritingRequest;
import com.alibaba.cloud.ai.example.patent.service.PatentWritingCoordinator;
import com.alibaba.cloud.ai.example.copilot.service.SseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 专利写作控制器
 * 提供专利写作任务的创建、执行和状态查询接口
 */
@RestController
@RequestMapping("/api/patent-writing")
public class PatentWritingController {

    private static final Logger logger = LoggerFactory.getLogger(PatentWritingController.class);

    @Autowired
    private PatentWritingCoordinator coordinator;

    @Autowired
    private SseService sseService;

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "ok");
        response.put("message", "专利写作助手服务正常运行");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }

    /**
     * 创建专利写作任务
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createPatentWritingTask(
            @RequestBody PatentWritingRequest request) {
        
        try {
            // 验证请求参数
            if (request.getInnovationDescription() == null || 
                request.getInnovationDescription().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", "技术创新描述不能为空"
                ));
            }

            // 生成任务ID
            String taskId = UUID.randomUUID().toString();

            logger.info("创建专利写作任务，任务ID: {}, 创新描述: {}", 
                       taskId, request.getInnovationDescription());

            // 启动专利写作任务
            coordinator.startPatentWriting(request, taskId);

            // 返回任务创建成功的响应
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("status", "processing");
            response.put("message", "专利写作任务已创建，请通过SSE连接获取实时进度");
            response.put("timestamp", System.currentTimeMillis());

            logger.info("专利写作任务创建成功，任务ID: {}", taskId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("创建专利写作任务失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "创建任务失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<Map<String, Object>> getTaskStatus(@PathVariable String taskId) {
        try {
            PatentWritingPlan plan = coordinator.getTaskStatus(taskId);

            if (plan == null) {
                return ResponseEntity.status(404).body(Map.of(
                    "status", "error",
                    "message", "任务不存在: " + taskId
                ));
            }

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("status", plan.getPlanStatus());
            response.put("patentTitle", plan.getPatentTitle());
            response.put("currentStage", plan.getCurrentStage());
            response.put("stages", plan.getStages());
            response.put("createdAt", plan.getCreatedAt());
            response.put("updatedAt", plan.getUpdatedAt());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取任务状态失败，任务ID: {}", taskId, e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "获取任务状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 创建SSE连接以接收任务状态更新
     */
    @GetMapping("/stream/{taskId}")
    public SseEmitter streamTaskUpdates(@PathVariable String taskId,
                                       @RequestParam(defaultValue = "patent-writing") String clientId) {
        logger.info("创建专利写作SSE连接，任务ID: {}, 客户端ID: {}", taskId, clientId);

        // 使用SseService创建连接
        SseEmitter emitter = sseService.createConnection(taskId, clientId);

        // 发送初始连接确认消息
        try {
            emitter.send(SseEmitter.event()
                .name("connected")
                .data("{\"message\":\"专利写作SSE连接已建立\",\"taskId\":\"" + taskId + 
                     "\",\"timestamp\":" + System.currentTimeMillis() + "}"));
        } catch (Exception e) {
            logger.error("发送SSE初始消息失败，任务ID: {}, 客户端ID: {}", taskId, clientId, e);
        }

        return emitter;
    }

    /**
     * 取消任务
     */
    @PostMapping("/cancel/{taskId}")
    public ResponseEntity<Map<String, Object>> cancelTask(@PathVariable String taskId) {
        try {
            boolean cancelled = coordinator.cancelTask(taskId);

            if (cancelled) {
                return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "任务已取消",
                    "taskId", taskId
                ));
            } else {
                return ResponseEntity.status(404).body(Map.of(
                    "status", "error",
                    "message", "任务不存在或无法取消"
                ));
            }

        } catch (Exception e) {
            logger.error("取消任务失败，任务ID: {}", taskId, e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "取消任务失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取所有活跃任务
     */
    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getActiveTasks() {
        try {
            Map<String, PatentWritingPlan> activeTasks = coordinator.getActiveTasks();

            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("activeTasks", activeTasks);
            response.put("count", activeTasks.size());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取活跃任务失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "获取活跃任务失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 重试失败的阶段
     */
    @PostMapping("/retry/{taskId}/{stageId}")
    public ResponseEntity<Map<String, Object>> retryStage(
            @PathVariable String taskId, 
            @PathVariable String stageId) {
        
        try {
            boolean retried = coordinator.retryStage(taskId, stageId);

            if (retried) {
                return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "阶段重试已启动",
                    "taskId", taskId,
                    "stageId", stageId
                ));
            } else {
                return ResponseEntity.status(404).body(Map.of(
                    "status", "error",
                    "message", "任务或阶段不存在"
                ));
            }

        } catch (Exception e) {
            logger.error("重试阶段失败，任务ID: {}, 阶段ID: {}", taskId, stageId, e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "重试阶段失败: " + e.getMessage()
            ));
        }
    }
}
