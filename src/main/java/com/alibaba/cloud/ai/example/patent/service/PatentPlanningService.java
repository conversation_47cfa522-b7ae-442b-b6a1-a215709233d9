package com.alibaba.cloud.ai.example.patent.service;

import com.alibaba.cloud.ai.example.patent.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 专利规划服务
 * 负责创建专利写作计划和阶段规划
 */
@Service
public class PatentPlanningService {

    private static final Logger logger = LoggerFactory.getLogger(PatentPlanningService.class);

    /**
     * 创建专利写作计划
     */
    public PatentWritingPlan createWritingPlan(PatentWritingRequest request, String taskId) {
        logger.info("创建专利写作计划，任务ID: {}", taskId);

        try {
            // 创建写作计划
            PatentWritingPlan plan = new PatentWritingPlan();
            plan.setTaskId(taskId);
            plan.setPatentTitle("待确定"); // 将在选题阶段确定
            plan.setPlanStatus("PLANNING");
            plan.setCurrentStage("TOPIC_SELECTION");
            plan.setCreatedAt(System.currentTimeMillis());
            plan.setUpdatedAt(System.currentTimeMillis());

            // 创建写作上下文
            PatentWritingContext context = createWritingContext(request);
            plan.setContext(context);

            // 创建写作阶段
            List<PatentWritingStage> stages = createWritingStages();
            plan.setStages(stages);

            logger.info("专利写作计划创建成功，任务ID: {}, 阶段数: {}", taskId, stages.size());

            return plan;

        } catch (Exception e) {
            logger.error("创建专利写作计划失败，任务ID: {}", taskId, e);
            throw new RuntimeException("创建专利写作计划失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建写作上下文
     */
    private PatentWritingContext createWritingContext(PatentWritingRequest request) {
        PatentWritingContext context = new PatentWritingContext();
        
        context.setInnovationDescription(request.getInnovationDescription());
        context.setTechnicalField(request.getPreferredField());
        context.setCoreInnovations(new ArrayList<>());
        context.setTechnicalProblems(new ArrayList<>());
        context.setAdditionalData(new HashMap<>());

        // 存储申请人和发明人信息
        if (request.getApplicantInfo() != null) {
            context.getAdditionalData().put("applicantInfo", request.getApplicantInfo());
        }
        if (request.getInventorInfo() != null) {
            context.getAdditionalData().put("inventorInfo", request.getInventorInfo());
        }
        if (request.getKeywords() != null) {
            context.getAdditionalData().put("keywords", request.getKeywords());
        }

        return context;
    }

    /**
     * 创建写作阶段
     */
    private List<PatentWritingStage> createWritingStages() {
        List<PatentWritingStage> stages = new ArrayList<>();

        // 阶段1：选题分析
        PatentWritingStage topicStage = createTopicSelectionStage();
        stages.add(topicStage);

        // 阶段2：背景技术
        PatentWritingStage backgroundStage = createBackgroundTechnologyStage();
        stages.add(backgroundStage);

        // 阶段3：发明目的
        PatentWritingStage purposeStage = createInventionPurposeStage();
        stages.add(purposeStage);

        // 阶段4：方案概述
        PatentWritingStage solutionStage = createSolutionOverviewStage();
        stages.add(solutionStage);

        return stages;
    }

    /**
     * 创建选题分析阶段
     */
    private PatentWritingStage createTopicSelectionStage() {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("TOPIC_SELECTION");
        stage.setStageName("选题分析");
        stage.setStageType("TOPIC_SELECTION");
        stage.setStageDescription("分析技术创新点，确定专利撰写方向和重点，识别核心技术特征");
        stage.setStatus("PENDING");
        stage.setOrder(1);
        return stage;
    }

    /**
     * 创建背景技术阶段
     */
    private PatentWritingStage createBackgroundTechnologyStage() {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("BACKGROUND_TECHNOLOGY");
        stage.setStageName("背景技术");
        stage.setStageType("BACKGROUND_TECHNOLOGY");
        stage.setStageDescription("深入分析技术背景，调研现有技术方案，识别技术问题和不足");
        stage.setStatus("PENDING");
        stage.setOrder(2);
        return stage;
    }

    /**
     * 创建发明目的阶段
     */
    private PatentWritingStage createInventionPurposeStage() {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("INVENTION_PURPOSE");
        stage.setStageName("发明目的");
        stage.setStageType("INVENTION_PURPOSE");
        stage.setStageDescription("明确发明目的，定义要解决的技术问题，概述解决方案和预期效果");
        stage.setStatus("PENDING");
        stage.setOrder(3);
        return stage;
    }

    /**
     * 创建方案概述阶段
     */
    private PatentWritingStage createSolutionOverviewStage() {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("SOLUTION_OVERVIEW");
        stage.setStageName("方案概述");
        stage.setStageType("SOLUTION_OVERVIEW");
        stage.setStageDescription("围绕创新点详细描述技术方案，设计具体实施例，构建权利要求要素");
        stage.setStatus("PENDING");
        stage.setOrder(4);
        return stage;
    }
}
