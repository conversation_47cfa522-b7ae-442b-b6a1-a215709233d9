package com.alibaba.cloud.ai.example.patent.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 专利写作请求
 * 用户创建专利写作任务时的请求参数
 */
@Data
public class PatentWritingRequest {
    
    /**
     * 技术创新描述（必填）
     * 用户对技术创新的详细描述
     */
    private String innovationDescription;
    
    /**
     * 申请人信息（可选）
     * 专利申请人的基本信息
     */
    private String applicantInfo;
    
    /**
     * 发明人信息（可选）
     * 发明人的基本信息
     */
    private String inventorInfo;
    
    /**
     * 偏好技术领域（可选）
     * 用户指定的技术领域偏好
     */
    private String preferredField;
    
    /**
     * 关键词列表（可选）
     * 与技术创新相关的关键词
     */
    private List<String> keywords;
    
    /**
     * 附加信息（可选）
     * 其他相关信息
     */
    private Map<String, Object> additionalInfo;
    
    /**
     * 验证请求参数的有效性
     */
    public boolean isValid() {
        return innovationDescription != null && !innovationDescription.trim().isEmpty();
    }
    
    /**
     * 获取创新描述的长度
     */
    public int getInnovationDescriptionLength() {
        return innovationDescription != null ? innovationDescription.length() : 0;
    }
    
    /**
     * 检查是否有申请人信息
     */
    public boolean hasApplicantInfo() {
        return applicantInfo != null && !applicantInfo.trim().isEmpty();
    }
    
    /**
     * 检查是否有发明人信息
     */
    public boolean hasInventorInfo() {
        return inventorInfo != null && !inventorInfo.trim().isEmpty();
    }
    
    /**
     * 检查是否有偏好技术领域
     */
    public boolean hasPreferredField() {
        return preferredField != null && !preferredField.trim().isEmpty();
    }
    
    /**
     * 检查是否有关键词
     */
    public boolean hasKeywords() {
        return keywords != null && !keywords.isEmpty();
    }
    
    /**
     * 检查是否有附加信息
     */
    public boolean hasAdditionalInfo() {
        return additionalInfo != null && !additionalInfo.isEmpty();
    }
    
    /**
     * 获取关键词数量
     */
    public int getKeywordCount() {
        return keywords != null ? keywords.size() : 0;
    }
}
