package com.alibaba.cloud.ai.example.patent.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 专利写作上下文
 * 存储专利写作过程中的上下文信息
 */
@Data
public class PatentWritingContext {
    
    /**
     * 技术创新描述
     */
    private String innovationDescription;
    
    /**
     * 技术领域
     */
    private String technicalField;
    
    /**
     * 核心创新点列表
     */
    private List<String> coreInnovations;
    
    /**
     * 背景技术分析结果
     */
    private String backgroundAnalysis;
    
    /**
     * 技术问题列表
     */
    private List<String> technicalProblems;
    
    /**
     * 发明目的
     */
    private String inventionPurpose;
    
    /**
     * 方案概述
     */
    private String solutionOverview;
    
    /**
     * 扩展数据
     * 用于存储其他相关信息，如申请人信息、发明人信息、关键词等
     */
    private Map<String, Object> additionalData;
    
    /**
     * 获取申请人信息
     */
    public String getApplicantInfo() {
        if (additionalData != null && additionalData.containsKey("applicantInfo")) {
            return (String) additionalData.get("applicantInfo");
        }
        return null;
    }
    
    /**
     * 设置申请人信息
     */
    public void setApplicantInfo(String applicantInfo) {
        if (additionalData != null) {
            additionalData.put("applicantInfo", applicantInfo);
        }
    }
    
    /**
     * 获取发明人信息
     */
    public String getInventorInfo() {
        if (additionalData != null && additionalData.containsKey("inventorInfo")) {
            return (String) additionalData.get("inventorInfo");
        }
        return null;
    }
    
    /**
     * 设置发明人信息
     */
    public void setInventorInfo(String inventorInfo) {
        if (additionalData != null) {
            additionalData.put("inventorInfo", inventorInfo);
        }
    }
    
    /**
     * 获取关键词列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getKeywords() {
        if (additionalData != null && additionalData.containsKey("keywords")) {
            return (List<String>) additionalData.get("keywords");
        }
        return null;
    }
    
    /**
     * 设置关键词列表
     */
    public void setKeywords(List<String> keywords) {
        if (additionalData != null) {
            additionalData.put("keywords", keywords);
        }
    }
    
    /**
     * 获取选题分析结果
     */
    public String getTopicAnalysisResult() {
        if (additionalData != null && additionalData.containsKey("topicAnalysisResult")) {
            return (String) additionalData.get("topicAnalysisResult");
        }
        return null;
    }
    
    /**
     * 设置选题分析结果
     */
    public void setTopicAnalysisResult(String topicAnalysisResult) {
        if (additionalData != null) {
            additionalData.put("topicAnalysisResult", topicAnalysisResult);
        }
    }
    
    /**
     * 获取最终专利文档
     */
    public String getFinalDocument() {
        if (additionalData != null && additionalData.containsKey("finalDocument")) {
            return (String) additionalData.get("finalDocument");
        }
        return null;
    }
    
    /**
     * 设置最终专利文档
     */
    public void setFinalDocument(String finalDocument) {
        if (additionalData != null) {
            additionalData.put("finalDocument", finalDocument);
        }
    }
    
    /**
     * 检查是否有技术创新描述
     */
    public boolean hasInnovationDescription() {
        return innovationDescription != null && !innovationDescription.trim().isEmpty();
    }
    
    /**
     * 检查是否有技术领域信息
     */
    public boolean hasTechnicalField() {
        return technicalField != null && !technicalField.trim().isEmpty();
    }
    
    /**
     * 检查是否有核心创新点
     */
    public boolean hasCoreInnovations() {
        return coreInnovations != null && !coreInnovations.isEmpty();
    }
    
    /**
     * 检查是否有背景分析
     */
    public boolean hasBackgroundAnalysis() {
        return backgroundAnalysis != null && !backgroundAnalysis.trim().isEmpty();
    }
    
    /**
     * 检查是否有技术问题
     */
    public boolean hasTechnicalProblems() {
        return technicalProblems != null && !technicalProblems.isEmpty();
    }
    
    /**
     * 检查是否有发明目的
     */
    public boolean hasInventionPurpose() {
        return inventionPurpose != null && !inventionPurpose.trim().isEmpty();
    }
    
    /**
     * 检查是否有方案概述
     */
    public boolean hasSolutionOverview() {
        return solutionOverview != null && !solutionOverview.trim().isEmpty();
    }
}
