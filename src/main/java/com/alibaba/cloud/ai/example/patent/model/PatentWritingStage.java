package com.alibaba.cloud.ai.example.patent.model;

import lombok.Data;

/**
 * 专利写作阶段
 * 存储单个专利写作阶段的信息
 */
@Data
public class PatentWritingStage {
    
    /**
     * 阶段ID
     */
    private String stageId;
    
    /**
     * 阶段名称
     */
    private String stageName;
    
    /**
     * 阶段类型
     * TOPIC_SELECTION - 选题分析
     * BACKGROUND_TECHNOLOGY - 背景技术
     * INVENTION_PURPOSE - 发明目的
     * SOLUTION_OVERVIEW - 方案概述
     */
    private String stageType;
    
    /**
     * 阶段描述
     */
    private String stageDescription;
    
    /**
     * 执行状态
     * PENDING - 待执行
     * EXECUTING - 执行中
     * COMPLETED - 已完成
     * FAILED - 执行失败
     */
    private String status;
    
    /**
     * 阶段执行结果
     */
    private String result;
    
    /**
     * 开始执行时间戳
     */
    private long startTime;
    
    /**
     * 结束执行时间戳
     */
    private long endTime;
    
    /**
     * 阶段执行顺序
     */
    private int order;
    
    /**
     * 检查阶段是否完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }
    
    /**
     * 检查阶段是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }
    
    /**
     * 检查阶段是否正在执行
     */
    public boolean isExecuting() {
        return "EXECUTING".equals(status);
    }
    
    /**
     * 检查阶段是否待执行
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }
    
    /**
     * 获取执行耗时（毫秒）
     */
    public long getExecutionDuration() {
        if (startTime > 0 && endTime > 0) {
            return endTime - startTime;
        }
        return 0;
    }
    
    /**
     * 获取执行耗时（秒）
     */
    public double getExecutionDurationInSeconds() {
        return getExecutionDuration() / 1000.0;
    }
    
    /**
     * 检查阶段是否有结果
     */
    public boolean hasResult() {
        return result != null && !result.trim().isEmpty();
    }
    
    /**
     * 获取结果长度
     */
    public int getResultLength() {
        return result != null ? result.length() : 0;
    }
}
