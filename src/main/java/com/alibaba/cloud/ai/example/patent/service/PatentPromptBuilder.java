package com.alibaba.cloud.ai.example.patent.service;

import com.alibaba.cloud.ai.example.patent.model.PatentWritingContext;
import org.springframework.stereotype.Component;

/**
 * 专利写作提示词构建器
 * 负责构建各个阶段的专业化AI提示词
 */
@Component
public class PatentPromptBuilder {

    /**
     * 构建选题分析提示词
     */
    public String buildTopicSelectionPrompt(String innovationDescription) {
        return String.format("""
            # 专利选题分析专家
            
            ## 角色定位
            你是一位资深的专利代理人，擅长从技术创新中识别专利价值点和撰写方向。
            
            ## 任务目标
            对用户提供的技术创新进行专业的专利选题分析，确定专利撰写的核心方向。
            
            ## 分析要求
            1. **技术领域识别**
               - 确定技术所属的主要领域和IPC分类
               - 识别相关的技术分支和应用场景
               - 分析技术的交叉领域特征
            
            2. **创新点提取**
               - 识别核心技术创新点（至少2-3个）
               - 分析每个创新点的技术特征和层次
               - 评估创新点的技术价值和独创性
            
            3. **专利性评估**
               - 初步判断新颖性潜力
               - 分析创造性的可能性
               - 评估实用性和产业应用前景
            
            ## 输出格式要求
            请严格按照以下格式输出分析结果：
            
            ### 技术领域
            [详细描述技术所属领域，包括主要技术分类和应用领域]
            
            ### 核心创新点
            1. **创新点1**: [创新点名称和简要描述]
               - 技术特征: [具体的技术特征和实现方式]
               - 技术原理: [技术原理和作用机制]
               - 技术价值: [相对于现有技术的价值和优势]
            
            2. **创新点2**: [创新点名称和简要描述]
               - 技术特征: [具体的技术特征和实现方式]
               - 技术原理: [技术原理和作用机制]
               - 技术价值: [相对于现有技术的价值和优势]
            
            3. **创新点3**: [如有第三个创新点，按同样格式描述]
               - 技术特征: [具体的技术特征和实现方式]
               - 技术原理: [技术原理和作用机制]
               - 技术价值: [相对于现有技术的价值和优势]
            
            ### 专利性分析
            - **新颖性评估**: [分析技术方案的新颖性，是否存在相同的现有技术]
            - **创造性评估**: [分析技术方案的创造性，是否对现有技术有实质性改进]
            - **实用性评估**: [分析技术方案的实用性和产业化可能性]
            
            ### 建议专利标题
            [基于创新点提出的专利标题，要求准确、简洁、突出技术特征]
            
            ### 撰写重点建议
            [针对后续专利撰写的重点方向和注意事项]
            
            ## 技术创新描述
            %s
            
            请基于以上技术创新描述，进行专业、详细的专利选题分析。
            """, innovationDescription);
    }

    /**
     * 构建背景技术分析提示词
     */
    public String buildBackgroundAnalysisPrompt(PatentWritingContext context) {
        return String.format("""
            # 专利背景技术分析专家
            
            ## 角色定位
            你是专利撰写专家，擅长技术背景调研和现有技术分析，能够为专利撰写提供坚实的技术基础。
            
            ## 任务目标
            基于前期选题分析结果，深入分析技术背景，识别现有技术和技术问题，为发明目的的阐述奠定基础。
            
            ## 已知信息
            - 技术领域: %s
            - 核心创新点: %s
            - 创新描述: %s
            
            ## 分析要求
            1. **技术背景梳理**
               - 描述技术发展的历史脉络
               - 分析当前技术发展水平和现状
               - 说明技术发展趋势和方向
            
            2. **现有技术分析**
               - 收集和分析相关的技术方案
               - 详细描述现有技术的工作原理
               - 客观分析现有技术的优点和局限性
            
            3. **技术问题识别**
               - 明确现有技术存在的具体问题
               - 分析问题产生的技术原因
               - 评估问题对技术应用的影响
            
            ## 输出格式要求
            请严格按照专利撰写规范，输出以下内容：
            
            ### 技术背景
            [详细描述技术发展背景和现状，要求客观、准确，字数300-500字]
            
            随着[技术领域]技术的不断发展，[描述技术发展历程和现状]。目前，[描述当前技术水平]。然而，[引出技术发展中遇到的挑战]。
            
            ### 现有技术
            #### 现有技术方案一
            - **技术原理**: [详细描述技术原理和实现方式]
            - **技术构成**: [描述技术方案的主要组成部分]
            - **工作流程**: [说明技术方案的工作过程]
            - **主要优点**: [客观分析技术方案的优势]
            - **存在问题**: [指出技术方案的不足和局限性]
            
            #### 现有技术方案二
            - **技术原理**: [详细描述技术原理和实现方式]
            - **技术构成**: [描述技术方案的主要组成部分]
            - **工作流程**: [说明技术方案的工作过程]
            - **主要优点**: [客观分析技术方案的优势]
            - **存在问题**: [指出技术方案的不足和局限性]
            
            #### 现有技术方案三（如适用）
            - **技术原理**: [详细描述技术原理和实现方式]
            - **技术构成**: [描述技术方案的主要组成部分]
            - **工作流程**: [说明技术方案的工作过程]
            - **主要优点**: [客观分析技术方案的优势]
            - **存在问题**: [指出技术方案的不足和局限性]
            
            ### 现有技术存在的技术问题
            基于以上现有技术分析，可以发现存在以下主要技术问题：
            
            1. **技术问题1**: [具体描述第一个技术问题]
               - 问题表现: [问题的具体表现形式]
               - 产生原因: [问题产生的技术原因]
               - 影响程度: [问题对技术应用的影响]
            
            2. **技术问题2**: [具体描述第二个技术问题]
               - 问题表现: [问题的具体表现形式]
               - 产生原因: [问题产生的技术原因]
               - 影响程度: [问题对技术应用的影响]
            
            3. **技术问题3**: [具体描述第三个技术问题]
               - 问题表现: [问题的具体表现形式]
               - 产生原因: [问题产生的技术原因]
               - 影响程度: [问题对技术应用的影响]
            
            ### 技术需求分析
            基于现有技术的不足，亟需解决以下技术需求：
            - [技术需求1]
            - [技术需求2]
            - [技术需求3]
            
            请确保分析客观、准确、专业，为后续发明目的的阐述提供有力支撑。
            """,
            context.getTechnicalField() != null ? context.getTechnicalField() : "相关技术领域",
            context.getCoreInnovations() != null ? String.join(", ", context.getCoreInnovations()) : "核心创新点",
            context.getInnovationDescription() != null ? context.getInnovationDescription() : "技术创新描述"
        );
    }

    /**
     * 构建发明目的提示词
     */
    public String buildInventionPurposePrompt(PatentWritingContext context) {
        return String.format("""
            # 专利发明目的定义专家
            
            ## 角色定位
            你是专利撰写专家，擅长基于技术背景分析精确定义发明目的，确保发明目的与技术问题紧密对应。
            
            ## 任务目标
            基于前期技术背景分析结果，明确定义本发明的目的、要解决的技术问题和预期的技术效果。
            
            ## 已知信息
            - 技术领域: %s
            - 核心创新点: %s
            - 技术背景分析: %s
            - 现有技术问题: %s
            
            ## 撰写要求
            1. **技术问题定义**
               - 问题表述准确、具体、客观
               - 与现有技术的不足直接对应
               - 具有明确的技术性特征
               - 避免功能性或效果性表述
            
            2. **发明目的阐述**
               - 目的明确、针对性强
               - 与识别的技术问题紧密关联
               - 体现技术进步性和创新性
               - 表述简洁、逻辑清晰
            
            3. **解决方案概述**
               - 简要说明解决技术问题的核心思路
               - 突出关键技术手段和创新点
               - 为后续详细技术方案描述铺垫
            
            4. **有益效果预期**
               - 效果与技术问题直接对应
               - 效果可量化或可验证
               - 体现相对于现有技术的优势
            
            ## 输出格式要求
            请严格按照专利撰写规范，输出以下内容：
            
            ### 发明要解决的技术问题
            现有技术存在以下需要解决的技术问题：
            
            1. [技术问题1的准确、具体描述，避免功能性表述]
            2. [技术问题2的准确、具体描述，避免功能性表述]  
            3. [技术问题3的准确、具体描述，避免功能性表述]
            
            ### 发明目的
            针对现有技术存在的上述技术问题，本发明的目的是提供一种[技术方案的准确名称]，以解决现有技术中存在的[核心技术问题]，实现[预期的技术目标]。
            
            ### 技术解决方案概述
            为实现上述发明目的，本发明采用以下技术方案：
            
            [简要描述解决技术问题的核心技术思路，包括：]
            - [关键技术手段1]
            - [关键技术手段2]  
            - [关键技术手段3]
            
            [核心技术路径的简要说明，100-200字]
            
            ### 预期的有益效果
            采用本发明的技术方案，相对于现有技术具有以下有益效果：
            
            1. **[有益效果1]**: [具体说明如何解决技术问题1，产生什么技术效果]
            
            2. **[有益效果2]**: [具体说明如何解决技术问题2，产生什么技术效果]
            
            3. **[有益效果3]**: [具体说明如何解决技术问题3，产生什么技术效果]
            
            4. **[综合技术效果]**: [说明整体技术方案带来的综合技术进步]
            
            请确保内容逻辑清晰，表述准确专业，符合专利撰写规范。
            """,
            context.getTechnicalField() != null ? context.getTechnicalField() : "相关技术领域",
            context.getCoreInnovations() != null ? String.join(", ", context.getCoreInnovations()) : "核心创新点",
            context.getBackgroundAnalysis() != null ? context.getBackgroundAnalysis() : "技术背景分析结果",
            context.getTechnicalProblems() != null ? String.join(", ", context.getTechnicalProblems()) : "现有技术问题"
        );
    }

    /**
     * 构建方案概述提示词
     */
    public String buildSolutionOverviewPrompt(PatentWritingContext context) {
        return String.format("""
            # 专利技术方案描述专家
            
            ## 角色定位
            你是专利撰写专家，擅长围绕核心创新点构建完整、详细的技术方案描述，为权利要求撰写提供充分支撑。
            
            ## 任务目标
            基于前期分析结果，围绕核心创新点详细描述技术方案，包括技术原理、实施方式和权利要求要素。
            
            ## 已知信息
            - 技术领域: %s
            - 核心创新点: %s
            - 发明目的: %s
            - 要解决的技术问题: %s
            - 技术背景: %s
            
            ## 撰写要求
            1. **围绕创新点展开**
               - 每个创新点都要详细阐述技术特征
               - 说明创新点的技术原理和作用机制
               - 体现创新点相对于现有技术的优势
            
            2. **技术方案完整性**
               - 整体技术架构清晰明确
               - 关键技术模块和组件明确
               - 技术实现路径具体可行
            
            3. **实施方式具体化**
               - 提供详细的具体实施例
               - 包含关键技术参数和数值范围
               - 考虑多种变形实施方式
            
            4. **权利要求支撑**
               - 为独立权利要求提供必要技术特征
               - 为从属权利要求提供进一步限定特征
               - 确保技术方案的保护范围合理
            
            ## 输出格式要求
            请严格按照专利撰写规范，输出以下内容：
            
            ### 技术方案总体描述
            [从整体上描述技术方案的构成、工作原理和技术路线，要求逻辑清晰、层次分明，200-300字]
            
            本发明提供的[技术方案名称]主要包括[主要技术模块]。其工作原理是[核心工作原理]。通过[关键技术手段]，实现了[技术目标]，有效解决了[技术问题]。
            
            ### 核心创新点详述
            #### 创新点一：[创新点名称]
            - **技术特征**: [详细描述技术特征的具体实现方式，包括结构、参数、工艺等]
            - **技术原理**: [深入阐述技术原理和作用机制，说明为什么这样设计]
            - **技术优势**: [具体说明相对于现有技术的优势和改进之处]
            - **实现方式**: [描述具体的实现方法、步骤或工艺流程]
            - **参数范围**: [如适用，给出关键参数的具体数值范围]
            
            #### 创新点二：[创新点名称]
            - **技术特征**: [详细描述技术特征的具体实现方式，包括结构、参数、工艺等]
            - **技术原理**: [深入阐述技术原理和作用机制，说明为什么这样设计]
            - **技术优势**: [具体说明相对于现有技术的优势和改进之处]
            - **实现方式**: [描述具体的实现方法、步骤或工艺流程]
            - **参数范围**: [如适用，给出关键参数的具体数值范围]
            
            #### 创新点三：[如有第三个创新点]
            - **技术特征**: [详细描述技术特征的具体实现方式，包括结构、参数、工艺等]
            - **技术原理**: [深入阐述技术原理和作用机制，说明为什么这样设计]
            - **技术优势**: [具体说明相对于现有技术的优势和改进之处]
            - **实现方式**: [描述具体的实现方法、步骤或工艺流程]
            - **参数范围**: [如适用，给出关键参数的具体数值范围]
            
            ### 具体实施例
            #### 实施例1（基本实施方式）
            - **应用场景**: [描述具体的应用场景和使用环境]
            - **技术配置**: [详细描述技术方案的具体配置和组成]
            - **关键参数**: [列出关键技术参数的具体数值]
            - **实施步骤**: [详细描述实施的具体步骤和流程]
            - **预期效果**: [说明预期达到的技术效果和性能指标]
            
            #### 实施例2（变形实施方式）
            - **变形特点**: [说明与基本实施方式的主要差异]
            - **适用场景**: [描述该变形方式适用的特殊场景]
            - **技术调整**: [详细说明技术方案的调整和改进内容]
            - **参数变化**: [说明关键参数的变化范围]
            - **效果差异**: [说明与基本实施方式在效果上的差异]
            
            #### 实施例3（扩展实施方式，如适用）
            - **扩展特点**: [说明技术方案的扩展特征]
            - **应用拓展**: [描述扩展后的应用范围]
            - **技术增强**: [说明增强的技术功能]
            - **性能提升**: [说明性能提升的具体表现]
            
            ### 权利要求要素提取
            #### 独立权利要求必要技术特征
            基于以上技术方案，独立权利要求应包含以下必要技术特征：
            1. [必要技术特征1：描述技术方案的基本构成要素]
            2. [必要技术特征2：描述关键的技术手段或结构]
            3. [必要技术特征3：描述核心的技术关系或连接方式]
            4. [必要技术特征4：描述重要的技术参数或条件]
            
            #### 从属权利要求进一步限定特征
            为了更好地保护技术方案，从属权利要求可包含以下进一步限定特征：
            1. [进一步限定特征1：对基本技术特征的具体化描述]
            2. [进一步限定特征2：对技术参数的具体数值限定]
            3. [进一步限定特征3：对实施方式的具体化描述]
            4. [进一步限定特征4：对技术效果的具体化描述]
            5. [进一步限定特征5：对应用场景的具体化描述]
            
            ### 技术方案的技术效果
            本发明的技术方案通过上述创新点的有机结合，实现了以下技术效果：
            1. [技术效果1及其实现机理]
            2. [技术效果2及其实现机理]
            3. [技术效果3及其实现机理]
            
            请确保技术方案描述详细、准确、完整，能够充分支撑权利要求的撰写，体现技术方案的创新性和实用性。
            """,
            context.getTechnicalField() != null ? context.getTechnicalField() : "相关技术领域",
            context.getCoreInnovations() != null ? String.join(", ", context.getCoreInnovations()) : "核心创新点",
            context.getInventionPurpose() != null ? context.getInventionPurpose() : "发明目的",
            context.getTechnicalProblems() != null ? String.join(", ", context.getTechnicalProblems()) : "技术问题",
            context.getBackgroundAnalysis() != null ? context.getBackgroundAnalysis() : "技术背景"
        );
    }
}
