package com.alibaba.cloud.ai.example.patent.service;

import com.alibaba.cloud.ai.example.patent.model.PatentWritingContext;
import com.alibaba.cloud.ai.example.patent.model.PatentWritingPlan;
import com.alibaba.cloud.ai.example.patent.model.PatentWritingStage;
import com.alibaba.cloud.ai.example.copilot.service.LlmService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;

/**
 * 专利写作阶段执行器
 * 负责执行具体的专利写作阶段
 */
@Service
public class PatentStageExecutor {

    private static final Logger logger = LoggerFactory.getLogger(PatentStageExecutor.class);

    @Autowired
    private PatentPromptBuilder promptBuilder;

    @Autowired
    private LlmService llmService;

    private final ExecutorService executorService = Executors.newCachedThreadPool();

    /**
     * 执行专利写作阶段
     */
    public void executeStage(String taskId, PatentWritingStage stage, 
                           BiConsumer<String, PatentWritingStage> onCompleted) {
        
        logger.info("开始执行专利写作阶段，任务ID: {}, 阶段: {}", taskId, stage.getStageName());

        // 异步执行阶段
        executorService.submit(() -> {
            try {
                // 更新阶段状态
                stage.setStatus("EXECUTING");
                stage.setStartTime(System.currentTimeMillis());

                // 根据阶段类型执行不同逻辑
                String result = executeStageByType(taskId, stage);

                // 更新阶段结果
                stage.setResult(result);
                stage.setStatus("COMPLETED");
                stage.setEndTime(System.currentTimeMillis());

                logger.info("专利写作阶段执行成功，任务ID: {}, 阶段: {}", taskId, stage.getStageName());

            } catch (Exception e) {
                logger.error("专利写作阶段执行失败，任务ID: {}, 阶段: {}", taskId, stage.getStageName(), e);
                
                stage.setStatus("FAILED");
                stage.setResult("执行失败: " + e.getMessage());
                stage.setEndTime(System.currentTimeMillis());
            }

            // 通知协调器阶段完成
            onCompleted.accept(taskId, stage);
        });
    }

    /**
     * 根据阶段类型执行不同逻辑
     */
    private String executeStageByType(String taskId, PatentWritingStage stage) {
        return switch (stage.getStageType()) {
            case "TOPIC_SELECTION" -> executeTopicSelection(taskId, stage);
            case "BACKGROUND_TECHNOLOGY" -> executeBackgroundAnalysis(taskId, stage);
            case "INVENTION_PURPOSE" -> executeInventionPurpose(taskId, stage);
            case "SOLUTION_OVERVIEW" -> executeSolutionOverview(taskId, stage);
            default -> throw new IllegalArgumentException("未知阶段类型: " + stage.getStageType());
        };
    }

    /**
     * 执行选题分析阶段
     */
    private String executeTopicSelection(String taskId, PatentWritingStage stage) {
        logger.info("执行选题分析阶段，任务ID: {}", taskId);

        // 获取上下文信息（这里需要从协调器获取，简化实现）
        String innovationDescription = getInnovationDescription(taskId);

        // 构建选题分析提示词
        String prompt = promptBuilder.buildTopicSelectionPrompt(innovationDescription);

        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();

        logger.info("选题分析完成，任务ID: {}, 结果长度: {}", taskId, result.length());

        return result;
    }

    /**
     * 执行背景技术分析阶段
     */
    private String executeBackgroundAnalysis(String taskId, PatentWritingStage stage) {
        logger.info("执行背景技术分析阶段，任务ID: {}", taskId);

        // 获取上下文信息
        PatentWritingContext context = getWritingContext(taskId);

        // 构建背景分析提示词
        String prompt = promptBuilder.buildBackgroundAnalysisPrompt(context);

        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();

        logger.info("背景技术分析完成，任务ID: {}, 结果长度: {}", taskId, result.length());

        return result;
    }

    /**
     * 执行发明目的阶段
     */
    private String executeInventionPurpose(String taskId, PatentWritingStage stage) {
        logger.info("执行发明目的阶段，任务ID: {}", taskId);

        // 获取上下文信息
        PatentWritingContext context = getWritingContext(taskId);

        // 构建发明目的提示词
        String prompt = promptBuilder.buildInventionPurposePrompt(context);

        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();

        logger.info("发明目的定义完成，任务ID: {}, 结果长度: {}", taskId, result.length());

        return result;
    }

    /**
     * 执行方案概述阶段
     */
    private String executeSolutionOverview(String taskId, PatentWritingStage stage) {
        logger.info("执行方案概述阶段，任务ID: {}", taskId);

        // 获取上下文信息
        PatentWritingContext context = getWritingContext(taskId);

        // 构建方案概述提示词
        String prompt = promptBuilder.buildSolutionOverviewPrompt(context);

        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();

        logger.info("方案概述完成，任务ID: {}, 结果长度: {}", taskId, result.length());

        return result;
    }

    /**
     * 获取创新描述（简化实现）
     */
    private String getInnovationDescription(String taskId) {
        // 这里应该从协调器或缓存中获取上下文信息
        // 简化实现，返回默认值
        return "技术创新描述";
    }

    /**
     * 获取写作上下文（简化实现）
     */
    private PatentWritingContext getWritingContext(String taskId) {
        // 这里应该从协调器获取完整的上下文信息
        // 简化实现，创建一个基本的上下文
        PatentWritingContext context = new PatentWritingContext();
        context.setInnovationDescription("技术创新描述");
        context.setTechnicalField("技术领域");
        return context;
    }
}
