package com.alibaba.cloud.ai.example.patent.service;

import com.alibaba.cloud.ai.example.patent.model.*;
import com.alibaba.cloud.ai.example.copilot.service.SseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 专利写作协调器
 * 负责协调整个专利写作流程的执行
 */
@Service
public class PatentWritingCoordinator {

    private static final Logger logger = LoggerFactory.getLogger(PatentWritingCoordinator.class);

    @Autowired
    private PatentPlanningService planningService;

    @Autowired
    private PatentStageExecutor stageExecutor;

    @Autowired
    private SseService sseService;

    private final Map<String, PatentWritingPlan> activePlans = new ConcurrentHashMap<>();
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    /**
     * 启动专利写作任务
     */
    public void startPatentWriting(PatentWritingRequest request, String taskId) {
        logger.info("启动专利写作任务，任务ID: {}", taskId);

        executorService.submit(() -> {
            try {
                // 1. 创建写作计划
                PatentWritingPlan plan = planningService.createWritingPlan(request, taskId);
                activePlans.put(taskId, plan);

                logger.info("专利写作计划创建完成，任务ID: {}, 阶段数: {}", 
                           taskId, plan.getStages().size());

                // 2. 通知前端计划已创建
                sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));

                // 3. 开始执行第一阶段
                executeNextStage(taskId);

            } catch (Exception e) {
                logger.error("专利写作任务启动失败，任务ID: {}", taskId, e);
                updatePlanStatus(taskId, "FAILED");
                
                // 通知前端任务失败
                if (activePlans.containsKey(taskId)) {
                    sseService.sendTaskUpdate(taskId, convertToTaskPlan(activePlans.get(taskId)));
                }
            }
        });
    }

    /**
     * 执行下一阶段
     */
    private void executeNextStage(String taskId) {
        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan == null) {
            logger.error("找不到专利写作计划，任务ID: {}", taskId);
            return;
        }

        PatentWritingStage nextStage = findNextStage(plan);
        if (nextStage != null) {
            logger.info("开始执行下一阶段，任务ID: {}, 阶段: {}", taskId, nextStage.getStageName());
            
            // 更新当前阶段
            plan.setCurrentStage(nextStage.getStageType());
            plan.setUpdatedAt(System.currentTimeMillis());
            
            // 执行阶段
            stageExecutor.executeStage(taskId, nextStage, this::onStageCompleted);
        } else {
            // 所有阶段完成
            completePlan(taskId);
        }
    }

    /**
     * 查找下一个待执行的阶段
     */
    private PatentWritingStage findNextStage(PatentWritingPlan plan) {
        return plan.getStages().stream()
            .filter(stage -> "PENDING".equals(stage.getStatus()))
            .min((s1, s2) -> Integer.compare(s1.getOrder(), s2.getOrder()))
            .orElse(null);
    }

    /**
     * 阶段完成回调
     */
    public void onStageCompleted(String taskId, PatentWritingStage stage) {
        logger.info("阶段执行完成，任务ID: {}, 阶段: {}, 状态: {}", 
                   taskId, stage.getStageName(), stage.getStatus());

        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan == null) {
            logger.error("找不到专利写作计划，任务ID: {}", taskId);
            return;
        }

        // 更新计划状态
        updatePlanWithStageResult(plan, stage);

        // 通知前端阶段完成
        sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));

        // 如果阶段成功完成，执行下一阶段
        if ("COMPLETED".equals(stage.getStatus())) {
            executeNextStage(taskId);
        } else if ("FAILED".equals(stage.getStatus())) {
            // 阶段失败，整个计划失败
            updatePlanStatus(taskId, "FAILED");
            sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
        }
    }

    /**
     * 完成写作计划
     */
    private void completePlan(String taskId) {
        logger.info("专利写作计划完成，任务ID: {}", taskId);

        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan != null) {
            plan.setPlanStatus("COMPLETED");
            plan.setUpdatedAt(System.currentTimeMillis());

            // 生成最终的专利文档
            generateFinalPatentDocument(plan);

            // 通知前端计划完成
            sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));

            logger.info("专利写作任务完全完成，任务ID: {}", taskId);
        }
    }

    /**
     * 生成最终专利文档
     */
    private void generateFinalPatentDocument(PatentWritingPlan plan) {
        StringBuilder documentBuilder = new StringBuilder();
        
        documentBuilder.append("# ").append(plan.getPatentTitle()).append("\n\n");
        
        for (PatentWritingStage stage : plan.getStages()) {
            if ("COMPLETED".equals(stage.getStatus()) && stage.getResult() != null) {
                documentBuilder.append("## ").append(stage.getStageName()).append("\n\n");
                documentBuilder.append(stage.getResult()).append("\n\n");
            }
        }

        String finalDocument = documentBuilder.toString();
        
        // 保存到上下文
        plan.getContext().getAdditionalData().put("finalDocument", finalDocument);
        
        logger.info("最终专利文档生成完成，任务ID: {}, 文档长度: {}", 
                   plan.getTaskId(), finalDocument.length());
    }

    /**
     * 更新计划状态
     */
    private void updatePlanStatus(String taskId, String status) {
        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan != null) {
            plan.setPlanStatus(status);
            plan.setUpdatedAt(System.currentTimeMillis());
        }
    }

    /**
     * 更新计划与阶段结果
     */
    private void updatePlanWithStageResult(PatentWritingPlan plan, PatentWritingStage stage) {
        // 更新上下文信息
        PatentWritingContext context = plan.getContext();
        
        switch (stage.getStageType()) {
            case "TOPIC_SELECTION":
                // 解析选题分析结果，更新上下文
                updateContextWithTopicResult(context, stage.getResult());
                break;
            case "BACKGROUND_TECHNOLOGY":
                // 更新背景技术分析结果
                context.setBackgroundAnalysis(stage.getResult());
                break;
            case "INVENTION_PURPOSE":
                // 更新发明目的
                context.setInventionPurpose(stage.getResult());
                break;
            case "SOLUTION_OVERVIEW":
                // 更新方案概述
                context.setSolutionOverview(stage.getResult());
                break;
        }
        
        plan.setUpdatedAt(System.currentTimeMillis());
    }

    /**
     * 更新上下文与选题结果
     */
    private void updateContextWithTopicResult(PatentWritingContext context, String result) {
        // 这里可以解析AI返回的结果，提取关键信息更新到上下文
        // 简化实现，直接将结果存储
        context.getAdditionalData().put("topicAnalysisResult", result);
        
        // 可以进一步解析结果，提取技术领域、创新点等信息
        // 这里简化处理
    }

    /**
     * 转换为TaskPlan格式（兼容现有SSE服务）
     */
    private Object convertToTaskPlan(PatentWritingPlan plan) {
        // 创建一个兼容的对象用于SSE传输
        Map<String, Object> taskPlan = new ConcurrentHashMap<>();
        taskPlan.put("taskId", plan.getTaskId());
        taskPlan.put("title", plan.getPatentTitle());
        taskPlan.put("description", "专利写作任务");
        taskPlan.put("planStatus", plan.getPlanStatus());
        taskPlan.put("currentStage", plan.getCurrentStage());
        taskPlan.put("stages", plan.getStages());
        taskPlan.put("createdAt", plan.getCreatedAt());
        taskPlan.put("updatedAt", plan.getUpdatedAt());
        
        return taskPlan;
    }

    /**
     * 获取任务状态
     */
    public PatentWritingPlan getTaskStatus(String taskId) {
        return activePlans.get(taskId);
    }

    /**
     * 获取所有活跃任务
     */
    public Map<String, PatentWritingPlan> getActiveTasks() {
        return new ConcurrentHashMap<>(activePlans);
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan != null && !"COMPLETED".equals(plan.getPlanStatus())) {
            plan.setPlanStatus("CANCELLED");
            plan.setUpdatedAt(System.currentTimeMillis());
            
            // 通知前端任务已取消
            sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
            
            logger.info("任务已取消，任务ID: {}", taskId);
            return true;
        }
        return false;
    }

    /**
     * 重试失败的阶段
     */
    public boolean retryStage(String taskId, String stageId) {
        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan == null) {
            return false;
        }

        PatentWritingStage stage = plan.getStages().stream()
            .filter(s -> stageId.equals(s.getStageId()))
            .findFirst()
            .orElse(null);

        if (stage != null && "FAILED".equals(stage.getStatus())) {
            // 重置阶段状态
            stage.setStatus("PENDING");
            stage.setResult(null);
            stage.setStartTime(0);
            stage.setEndTime(0);
            
            // 重新执行阶段
            stageExecutor.executeStage(taskId, stage, this::onStageCompleted);
            
            logger.info("重试阶段，任务ID: {}, 阶段: {}", taskId, stage.getStageName());
            return true;
        }
        
        return false;
    }
}
