package com.alibaba.cloud.ai.example.patent.model;

import lombok.Data;

import java.util.List;

/**
 * 专利写作计划
 * 存储整个专利写作任务的计划信息
 */
@Data
public class PatentWritingPlan {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 专利标题
     */
    private String patentTitle;
    
    /**
     * 当前执行阶段
     */
    private String currentStage;
    
    /**
     * 计划状态
     * PLANNING - 规划中
     * EXECUTING - 执行中
     * COMPLETED - 已完成
     * FAILED - 执行失败
     * CANCELLED - 已取消
     */
    private String planStatus;
    
    /**
     * 写作阶段列表
     */
    private List<PatentWritingStage> stages;
    
    /**
     * 写作上下文
     */
    private PatentWritingContext context;
    
    /**
     * 创建时间戳
     */
    private long createdAt;
    
    /**
     * 更新时间戳
     */
    private long updatedAt;
    
    /**
     * 根据阶段类型获取阶段
     */
    public PatentWritingStage getStageByType(String stageType) {
        if (stages == null) {
            return null;
        }
        
        return stages.stream()
            .filter(stage -> stageType.equals(stage.getStageType()))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 根据阶段ID获取阶段
     */
    public PatentWritingStage getStageById(String stageId) {
        if (stages == null) {
            return null;
        }
        
        return stages.stream()
            .filter(stage -> stageId.equals(stage.getStageId()))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 获取当前执行的阶段
     */
    public PatentWritingStage getCurrentStageObject() {
        if (currentStage == null) {
            return null;
        }
        
        return getStageByType(currentStage);
    }
    
    /**
     * 检查计划是否完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(planStatus);
    }
    
    /**
     * 检查计划是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(planStatus);
    }
    
    /**
     * 检查计划是否被取消
     */
    public boolean isCancelled() {
        return "CANCELLED".equals(planStatus);
    }
    
    /**
     * 获取已完成的阶段数量
     */
    public int getCompletedStageCount() {
        if (stages == null) {
            return 0;
        }
        
        return (int) stages.stream()
            .filter(stage -> "COMPLETED".equals(stage.getStatus()))
            .count();
    }
    
    /**
     * 获取总阶段数量
     */
    public int getTotalStageCount() {
        return stages != null ? stages.size() : 0;
    }
    
    /**
     * 计算完成进度百分比
     */
    public double getProgressPercentage() {
        int total = getTotalStageCount();
        if (total == 0) {
            return 0.0;
        }
        
        int completed = getCompletedStageCount();
        return (double) completed / total * 100.0;
    }
}
