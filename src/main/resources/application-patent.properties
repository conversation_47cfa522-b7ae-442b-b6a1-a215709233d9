# ??????????

# ??????
spring.application.name=patent-writing-assistant
server.port=8080
debug=false

# Web????
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.enabled=true

# AI????
spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode
spring.ai.openai.api-key=${DASHSCOPE_API_KEY:your-api-key-here}
spring.ai.openai.chat.options.model=qwen-plus

# MCP??
spring.ai.mcp.client.stdio.servers-configuration=classpath:/mcp-servers-config.json
spring.ai.mcp.client.timeout=60000
spring.ai.mcp.client.stdio.timeout=60000

# ??????????
patent.writing.max-innovation-description-length=5000
patent.writing.max-concurrent-tasks=10
patent.writing.stage-timeout-minutes=30
patent.writing.enable-auto-retry=true
patent.writing.max-retry-attempts=3

# ????
logging.level.root=INFO
logging.level.com.alibaba.cloud.ai.example.patent=DEBUG
logging.level.org.springframework.ai=INFO
logging.level.org.springframework.web=INFO

# SSE??
spring.mvc.async.request-timeout=300000

# ?????
patent.writing.thread-pool.core-size=5
patent.writing.thread-pool.max-size=20
patent.writing.thread-pool.queue-capacity=100
patent.writing.thread-pool.keep-alive-seconds=60
