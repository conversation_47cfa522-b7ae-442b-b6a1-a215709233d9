debug=true
spring.application.name=mcp
spring.main.web-application-type=servlet


spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.enabled=true

spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode
spring.ai.openai.api-key=${DASHSCOPE_API_KEY:sk-311a368820f04fea86a9f163290cbd09}
spring.ai.openai.chat.options.model=qwen-plus

# MCPéç½®
spring.ai.mcp.client.stdio.servers-configuration=classpath:/mcp-servers-config.json
spring.ai.mcp.client.timeout=60000
spring.ai.mcp.client.stdio.timeout=60000

# Logging configuration
logging.level.root=INFO
logging.level.org.springframework.ai.mcp=INFO
logging.level.org.springframework.ai.mcp.client.transport.StdioClientTransport=INFO
logging.level.org.springframework.ai.mcp.samples.brave=INFO
logging.level.io.modelcontextprotocol.client=INFO
logging.level.io.modelcontextprotocol.spec=INFO
