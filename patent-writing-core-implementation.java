// 专利写作助手核心实现代码

// ================================
// 1. 专利写作计划服务
// ================================

@Service
public class PatentWritingPlanningService {
    
    private static final Logger logger = LoggerFactory.getLogger(PatentWritingPlanningService.class);
    
    @Autowired
    private LlmService llmService;
    
    @Autowired
    private PatentWritingPromptBuilder promptBuilder;
    
    /**
     * 基于创新点创建专利写作计划
     */
    public PatentWritingPlan createInnovationBasedPlan(String innovationDescription, String taskId) {
        logger.info("创建基于创新点的专利写作计划，任务ID: {}", taskId);
        
        try {
            // 1. 构建选题分析提示词
            String topicPrompt = promptBuilder.buildTopicAnalysisPrompt(innovationDescription);
            
            // 2. 调用AI进行选题分析
            ChatResponse response = llmService.getChatClient().call(new Prompt(topicPrompt));
            String analysisResult = response.getResult().getOutput().getContent();
            
            // 3. 解析分析结果
            TopicAnalysisResult topicAnalysis = parseTopicAnalysis(analysisResult);
            
            // 4. 创建写作计划
            PatentWritingPlan plan = new PatentWritingPlan();
            plan.setTaskId(taskId);
            plan.setPatentTitle(topicAnalysis.getPatentTitle());
            plan.setInnovationCore(String.join(", ", topicAnalysis.getCoreInnovations()));
            plan.setTechnicalField(topicAnalysis.getTechnicalField());
            plan.setPlanStatus("PLANNING");
            
            // 5. 生成写作阶段
            List<PatentWritingStage> stages = generateWritingStages(topicAnalysis);
            plan.setStages(stages);
            plan.setCurrentStage("TOPIC_SELECTION");
            
            // 6. 初始化上下文
            PatentWritingContext context = initializeContext(innovationDescription, topicAnalysis);
            plan.setContext(context);
            
            logger.info("专利写作计划创建成功，任务ID: {}, 阶段数: {}", taskId, stages.size());
            return plan;
            
        } catch (Exception e) {
            logger.error("创建专利写作计划失败，任务ID: {}", taskId, e);
            throw new RuntimeException("创建专利写作计划失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成写作阶段
     */
    private List<PatentWritingStage> generateWritingStages(TopicAnalysisResult topicAnalysis) {
        List<PatentWritingStage> stages = new ArrayList<>();
        
        // 阶段1：选题分析
        PatentWritingStage topicStage = createTopicSelectionStage(topicAnalysis);
        stages.add(topicStage);
        
        // 阶段2：背景分析
        PatentWritingStage backgroundStage = createBackgroundAnalysisStage(topicAnalysis);
        stages.add(backgroundStage);
        
        // 阶段3：发明目的
        PatentWritingStage purposeStage = createInventionPurposeStage(topicAnalysis);
        stages.add(purposeStage);
        
        // 阶段4：方案概述
        PatentWritingStage solutionStage = createSolutionOverviewStage(topicAnalysis);
        stages.add(solutionStage);
        
        return stages;
    }
    
    /**
     * 创建选题分析阶段
     */
    private PatentWritingStage createTopicSelectionStage(TopicAnalysisResult analysis) {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("TOPIC_SELECTION");
        stage.setStageName("选题分析");
        stage.setStageType("TOPIC_SELECTION");
        stage.setStageDescription("分析技术创新点，确定专利撰写方向和重点");
        stage.setStageStatus("PENDING");
        
        List<PatentWritingStep> steps = new ArrayList<>();
        
        // 步骤1：技术领域识别
        PatentWritingStep step1 = new PatentWritingStep();
        step1.setStepIndex(1);
        step1.setStepName("技术领域识别");
        step1.setStepType("ANALYSIS");
        step1.setStepRequirement("识别技术所属领域，确定IPC分类，分析技术特征");
        step1.setToolName("patent-classification");
        step1.setStatus("PENDING");
        steps.add(step1);
        
        // 步骤2：创新点提取
        PatentWritingStep step2 = new PatentWritingStep();
        step2.setStepIndex(2);
        step2.setStepName("创新点提取");
        step2.setStepType("ANALYSIS");
        step2.setStepRequirement("从技术描述中提取核心创新点，分析创新的技术层次和独创性");
        step2.setStatus("PENDING");
        steps.add(step2);
        
        // 步骤3：专利性评估
        PatentWritingStep step3 = new PatentWritingStep();
        step3.setStepIndex(3);
        step3.setStepName("专利性初步评估");
        step3.setStepType("ANALYSIS");
        step3.setStepRequirement("评估技术方案的新颖性、创造性和实用性");
        step3.setToolName("patent-search");
        step3.setStatus("PENDING");
        steps.add(step3);
        
        stage.setSteps(steps);
        return stage;
    }
    
    /**
     * 创建背景分析阶段
     */
    private PatentWritingStage createBackgroundAnalysisStage(TopicAnalysisResult analysis) {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("BACKGROUND_ANALYSIS");
        stage.setStageName("背景分析");
        stage.setStageType("BACKGROUND_ANALYSIS");
        stage.setStageDescription("深入分析技术背景，识别现有技术和技术问题");
        stage.setStageStatus("PENDING");
        
        List<PatentWritingStep> steps = new ArrayList<>();
        
        // 步骤1：技术背景调研
        PatentWritingStep step1 = new PatentWritingStep();
        step1.setStepIndex(1);
        step1.setStepName("技术背景调研");
        step1.setStepType("RESEARCH");
        step1.setStepRequirement("调研技术发展历程，分析当前技术水平和发展趋势");
        step1.setToolName("patent-search");
        step1.setStatus("PENDING");
        steps.add(step1);
        
        // 步骤2：现有技术分析
        PatentWritingStep step2 = new PatentWritingStep();
        step2.setStepIndex(2);
        step2.setStepName("现有技术分析");
        step2.setStepType("ANALYSIS");
        step2.setStepRequirement("收集和分析相关技术方案，识别技术优缺点和空白点");
        step2.setToolName("patent-analysis");
        step2.setStatus("PENDING");
        steps.add(step2);
        
        // 步骤3：技术问题识别
        PatentWritingStep step3 = new PatentWritingStep();
        step3.setStepIndex(3);
        step3.setStepName("技术问题识别");
        step3.setStepType("ANALYSIS");
        step3.setStepRequirement("基于现有技术分析，识别技术瓶颈和用户痛点");
        step3.setStatus("PENDING");
        steps.add(step3);
        
        // 步骤4：相似专利检索
        PatentWritingStep step4 = new PatentWritingStep();
        step4.setStepIndex(4);
        step4.setStepName("相似专利检索");
        step4.setStepType("RESEARCH");
        step4.setStepRequirement("检索相似专利，分析专利布局和技术差异");
        step4.setToolName("patent-search");
        step4.setStatus("PENDING");
        steps.add(step4);
        
        stage.setSteps(steps);
        return stage;
    }
    
    /**
     * 创建发明目的阶段
     */
    private PatentWritingStage createInventionPurposeStage(TopicAnalysisResult analysis) {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("INVENTION_PURPOSE");
        stage.setStageName("发明目的");
        stage.setStageType("INVENTION_PURPOSE");
        stage.setStageDescription("明确发明目的，定义技术问题和解决方案");
        stage.setStageStatus("PENDING");
        
        List<PatentWritingStep> steps = new ArrayList<>();
        
        // 步骤1：技术问题定义
        PatentWritingStep step1 = new PatentWritingStep();
        step1.setStepIndex(1);
        step1.setStepName("技术问题定义");
        step1.setStepType("DEFINITION");
        step1.setStepRequirement("基于背景分析，准确定义要解决的技术问题");
        step1.setStatus("PENDING");
        steps.add(step1);
        
        // 步骤2：解决方案概述
        PatentWritingStep step2 = new PatentWritingStep();
        step2.setStepIndex(2);
        step2.setStepName("解决方案概述");
        step2.setStepType("DESIGN");
        step2.setStepRequirement("概述技术解决方案的核心思路和关键技术");
        step2.setStatus("PENDING");
        steps.add(step2);
        
        // 步骤3：技术效果预期
        PatentWritingStep step3 = new PatentWritingStep();
        step3.setStepIndex(3);
        step3.setStepName("技术效果预期");
        step3.setStepType("ANALYSIS");
        step3.setStepRequirement("分析技术方案的预期效果和技术优势");
        step3.setStatus("PENDING");
        steps.add(step3);
        
        stage.setSteps(steps);
        return stage;
    }
    
    /**
     * 创建方案概述阶段
     */
    private PatentWritingStage createSolutionOverviewStage(TopicAnalysisResult analysis) {
        PatentWritingStage stage = new PatentWritingStage();
        stage.setStageId("SOLUTION_OVERVIEW");
        stage.setStageName("方案概述");
        stage.setStageType("SOLUTION_OVERVIEW");
        stage.setStageDescription("围绕创新点详细描述技术方案和实施方式");
        stage.setStageStatus("PENDING");
        
        List<PatentWritingStep> steps = new ArrayList<>();
        
        // 步骤1：创新点详细描述
        PatentWritingStep step1 = new PatentWritingStep();
        step1.setStepIndex(1);
        step1.setStepName("创新点详细描述");
        step1.setStepType("DESCRIPTION");
        step1.setStepRequirement("围绕每个创新点，详细描述技术特征、原理和优势");
        step1.setStatus("PENDING");
        steps.add(step1);
        
        // 步骤2：技术方案阐述
        PatentWritingStep step2 = new PatentWritingStep();
        step2.setStepIndex(2);
        step2.setStepName("技术方案阐述");
        step2.setStepType("DESCRIPTION");
        step2.setStepRequirement("描述整体技术架构、关键模块和实现路径");
        step2.setStatus("PENDING");
        steps.add(step2);
        
        // 步骤3：实施方式规划
        PatentWritingStep step3 = new PatentWritingStep();
        step3.setStepIndex(3);
        step3.setStepName("实施方式规划");
        step3.setStepType("DESIGN");
        step3.setStepRequirement("设计具体实施例，确定参数范围和变形方式");
        step3.setStatus("PENDING");
        steps.add(step3);
        
        // 步骤4：权利要求构建
        PatentWritingStep step4 = new PatentWritingStep();
        step4.setStepIndex(4);
        step4.setStepName("权利要求构建");
        step4.setStepType("DRAFTING");
        step4.setStepRequirement("基于技术方案构建独立和从属权利要求");
        step4.setToolName("patent-formatting");
        step4.setStatus("PENDING");
        steps.add(step4);
        
        stage.setSteps(steps);
        return stage;
    }
}

// ================================
// 2. 专利写作协调器
// ================================

@Service
public class PatentWritingCoordinator {
    
    private static final Logger logger = LoggerFactory.getLogger(PatentWritingCoordinator.class);
    
    @Autowired
    private PatentWritingPlanningService planningService;
    
    @Autowired
    private PatentWritingPromptBuilder promptBuilder;
    
    @Autowired
    private LlmService llmService;
    
    @Autowired
    private SseService sseService;
    
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    private final Map<String, PatentWritingPlan> activePlans = new ConcurrentHashMap<>();
    
    /**
     * 启动专利写作任务
     */
    public void startPatentWriting(PatentWritingRequest request, String taskId) {
        logger.info("启动专利写作任务，任务ID: {}", taskId);
        
        executorService.submit(() -> {
            try {
                // 1. 创建写作计划
                PatentWritingPlan plan = planningService.createInnovationBasedPlan(
                    request.getInnovationDescription(), taskId);
                activePlans.put(taskId, plan);
                
                // 2. 通知前端计划已创建
                sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
                
                // 3. 开始执行第一阶段
                executeStage(taskId, "TOPIC_SELECTION");
                
            } catch (Exception e) {
                logger.error("专利写作任务启动失败，任务ID: {}", taskId, e);
                updatePlanStatus(taskId, "FAILED");
            }
        });
    }
    
    /**
     * 执行写作阶段
     */
    private void executeStage(String taskId, String stageType) {
        PatentWritingPlan plan = activePlans.get(taskId);
        if (plan == null) {
            logger.error("找不到写作计划，任务ID: {}", taskId);
            return;
        }
        
        PatentWritingStage stage = plan.getStageByType(stageType);
        if (stage == null) {
            logger.error("找不到写作阶段，任务ID: {}, 阶段类型: {}", taskId, stageType);
            return;
        }
        
        logger.info("开始执行写作阶段，任务ID: {}, 阶段: {}", taskId, stage.getStageName());
        
        // 更新阶段状态
        stage.setStageStatus("EXECUTING");
        plan.setCurrentStage(stageType);
        sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
        
        // 执行阶段中的所有步骤
        for (PatentWritingStep step : stage.getSteps()) {
            executeStep(taskId, step);
        }
        
        // 阶段完成处理
        completeStage(taskId, stage);
    }
    
    /**
     * 执行写作步骤
     */
    private void executeStep(String taskId, PatentWritingStep step) {
        logger.info("执行写作步骤，任务ID: {}, 步骤: {}", taskId, step.getStepName());
        
        PatentWritingPlan plan = activePlans.get(taskId);
        
        // 更新步骤状态
        step.setStatus("EXECUTING");
        step.setStartTime(System.currentTimeMillis());
        sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
        
        try {
            // 构建步骤执行提示词
            String prompt = promptBuilder.buildStepExecutionPrompt(step, plan.getContext());
            
            // 调用AI执行步骤
            ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
            String result = response.getResult().getOutput().getContent();
            
            // 更新步骤结果
            step.setStepResult(result);
            step.setStatus("COMPLETED");
            step.setEndTime(System.currentTimeMillis());
            
            // 更新上下文
            updateContext(taskId, step, result);
            
            logger.info("写作步骤执行成功，任务ID: {}, 步骤: {}", taskId, step.getStepName());
            
        } catch (Exception e) {
            logger.error("写作步骤执行失败，任务ID: {}, 步骤: {}", taskId, step.getStepName(), e);
            step.setStatus("FAILED");
            step.setStepResult("执行失败: " + e.getMessage());
            step.setEndTime(System.currentTimeMillis());
        }
        
        // 通知前端步骤更新
        sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
    }
    
    /**
     * 完成写作阶段
     */
    private void completeStage(String taskId, PatentWritingStage stage) {
        logger.info("完成写作阶段，任务ID: {}, 阶段: {}", taskId, stage.getStageName());
        
        PatentWritingPlan plan = activePlans.get(taskId);
        
        // 检查阶段中所有步骤是否完成
        boolean allCompleted = stage.getSteps().stream()
            .allMatch(step -> "COMPLETED".equals(step.getStatus()));
        
        if (allCompleted) {
            stage.setStageStatus("COMPLETED");
            
            // 收集阶段结果
            String stageResult = collectStageResult(stage);
            stage.setStageResult(stageResult);
            
            // 进入下一阶段
            String nextStageType = getNextStageType(stage.getStageType());
            if (nextStageType != null) {
                executeStage(taskId, nextStageType);
            } else {
                // 所有阶段完成
                completePlan(taskId);
            }
        } else {
            stage.setStageStatus("FAILED");
            updatePlanStatus(taskId, "FAILED");
        }
        
        sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
    }
    
    /**
     * 完成写作计划
     */
    private void completePlan(String taskId) {
        logger.info("专利写作计划完成，任务ID: {}", taskId);
        
        PatentWritingPlan plan = activePlans.get(taskId);
        plan.setPlanStatus("COMPLETED");
        
        // 生成最终的专利文档
        generateFinalPatentDocument(taskId);
        
        sseService.sendTaskUpdate(taskId, convertToTaskPlan(plan));
    }
    
    /**
     * 生成最终专利文档
     */
    private void generateFinalPatentDocument(String taskId) {
        PatentWritingPlan plan = activePlans.get(taskId);
        
        // 收集所有阶段的结果
        StringBuilder documentBuilder = new StringBuilder();
        
        for (PatentWritingStage stage : plan.getStages()) {
            documentBuilder.append("## ").append(stage.getStageName()).append("\n\n");
            documentBuilder.append(stage.getStageResult()).append("\n\n");
        }
        
        String finalDocument = documentBuilder.toString();
        
        // 保存文档到上下文
        plan.getContext().getExtraData().put("finalDocument", finalDocument);
        
        logger.info("最终专利文档生成完成，任务ID: {}, 文档长度: {}", taskId, finalDocument.length());
    }
}

// ================================
// 3. 专利写作提示词构建器
// ================================

@Component
public class PatentWritingPromptBuilder {
    
    /**
     * 构建选题分析提示词
     */
    public String buildTopicAnalysisPrompt(String innovationDescription) {
        return String.format("""
            # 专利选题分析专家
            
            ## 角色定位
            你是一位资深的专利代理人和技术专家，擅长从技术创新中识别专利价值和撰写方向。
            
            ## 任务目标
            基于用户提供的技术创新描述，进行专业的专利选题分析，确定专利撰写的核心方向。
            
            ## 分析维度
            1. **技术领域识别**
               - 确定技术所属的IPC分类
               - 识别相关的技术子领域
               - 分析技术的交叉领域特征
            
            2. **创新点提取**
               - 识别核心技术创新点
               - 分析创新的技术层次
               - 评估创新的独创性程度
            
            3. **专利性初步评估**
               - 新颖性初步判断
               - 创造性潜力分析
               - 实用性评估
            
            ## 输出要求
            请以JSON格式输出分析结果：
            {
                "patentTitle": "建议的专利标题",
                "technicalField": "技术领域",
                "ipcClassification": "IPC分类号",
                "coreInnovations": ["核心创新点1", "核心创新点2"],
                "technicalLayers": ["技术层次1", "技术层次2"],
                "patentabilityAssessment": {
                    "novelty": "新颖性评估",
                    "inventiveness": "创造性评估",
                    "utility": "实用性评估"
                },
                "recommendedFocus": "建议的撰写重点",
                "nextSteps": ["下一步行动1", "下一步行动2"]
            }
            
            ## 用户输入的技术创新描述
            %s
            """, innovationDescription);
    }
    
    /**
     * 构建步骤执行提示词
     */
    public String buildStepExecutionPrompt(PatentWritingStep step, PatentWritingContext context) {
        String basePrompt = """
            # 专利写作步骤执行专家
            
            ## 当前任务
            步骤类型: %s
            步骤名称: %s
            步骤要求: %s
            
            ## 上下文信息
            技术领域: %s
            核心创新点: %s
            
            ## 执行要求
            请根据步骤要求，结合上下文信息，提供专业、详细的输出。
            确保输出符合专利文档的专业要求和格式规范。
            
            请直接提供结果，无需解释思考过程。
            """;
        
        return String.format(basePrompt,
            step.getStepType(),
            step.getStepName(),
            step.getStepRequirement(),
            context.getTechnicalField(),
            context.getInnovationDescription()
        );
    }
}

// ================================
// 4. 数据模型
// ================================

@Data
public class PatentWritingPlan {
    private String taskId;
    private String patentTitle;
    private String innovationCore;
    private String technicalField;
    private List<PatentWritingStage> stages = new ArrayList<>();
    private PatentWritingContext context;
    private String currentStage;
    private String planStatus;
    private long createdAt = System.currentTimeMillis();
    private long updatedAt = System.currentTimeMillis();
    
    public PatentWritingStage getStageByType(String stageType) {
        return stages.stream()
            .filter(stage -> stageType.equals(stage.getStageType()))
            .findFirst()
            .orElse(null);
    }
}

@Data
public class PatentWritingStage {
    private String stageId;
    private String stageName;
    private String stageType;
    private String stageDescription;
    private List<PatentWritingStep> steps = new ArrayList<>();
    private String stageStatus;
    private String stageResult;
    private Map<String, Object> stageData = new HashMap<>();
}

@Data
public class PatentWritingStep {
    private int stepIndex;
    private String stepName;
    private String stepType;
    private String stepRequirement;
    private String toolName;
    private String stepResult;
    private String status = "PENDING";
    private long startTime;
    private long endTime;
    private Map<String, Object> stepData = new HashMap<>();
}

@Data
public class PatentWritingContext {
    private String innovationDescription;
    private String technicalField;
    private String applicantInfo;
    private String inventorInfo;
    private List<String> keywords = new ArrayList<>();
    private Map<String, String> priorArt = new HashMap<>();
    private List<String> technicalProblems = new ArrayList<>();
    private List<String> technicalEffects = new ArrayList<>();
    private Map<String, Object> extraData = new HashMap<>();
}

@Data
public class PatentWritingRequest {
    private String innovationDescription;
    private String applicantInfo;
    private String inventorInfo;
    private String technicalField;
    private List<String> keywords;
    private Map<String, Object> additionalInfo;
}

@Data
public class TopicAnalysisResult {
    private String patentTitle;
    private String technicalField;
    private String ipcClassification;
    private List<String> coreInnovations;
    private List<String> technicalLayers;
    private PatentabilityAssessment patentabilityAssessment;
    private String recommendedFocus;
    private List<String> nextSteps;
}

@Data
public class PatentabilityAssessment {
    private String novelty;
    private String inventiveness;
    private String utility;
}
