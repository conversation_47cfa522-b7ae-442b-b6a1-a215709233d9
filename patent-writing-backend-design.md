# 专利写作助手后端设计

## 🎯 系统架构

### 核心组件

```
专利写作助手后端架构
├── Controller层
│   └── PatentWritingController.java     # 专利写作API控制器
├── Service层
│   ├── PatentWritingService.java        # 专利写作核心服务
│   ├── PatentPlanningService.java       # 专利规划服务
│   └── PatentPromptService.java         # 专利提示词服务
├── Planning层
│   ├── PatentWritingCoordinator.java    # 专利写作协调器
│   ├── PatentStageExecutor.java         # 阶段执行器
│   └── PatentPromptBuilder.java         # 提示词构建器
├── Model层
│   ├── PatentWritingPlan.java           # 专利写作计划
│   ├── PatentWritingStage.java          # 专利写作阶段
│   └── PatentWritingContext.java        # 专利写作上下文
└── Utils层
    ├── PatentFormatter.java             # 专利格式化工具
    └── PatentValidator.java             # 专利验证工具
```

## 📝 专利写作流程

### 阶段定义

```
专利写作流程
├── 阶段1: 选题分析 (TOPIC_SELECTION)
│   ├── 技术领域识别
│   ├── 创新点提取
│   └── 专利性评估
├── 阶段2: 背景技术 (BACKGROUND_TECHNOLOGY)
│   ├── 技术背景调研
│   ├── 现有技术分析
│   └── 技术问题识别
├── 阶段3: 发明目的 (INVENTION_PURPOSE)
│   ├── 技术问题定义
│   ├── 解决方案概述
│   └── 技术效果说明
└── 阶段4: 方案概述 (SOLUTION_OVERVIEW)
    ├── 创新点详述
    ├── 技术方案描述
    ├── 实施方式设计
    └── 权利要求构建
```

## 🔧 核心实现

### 1. 专利写作控制器

```java
@RestController
@RequestMapping("/api/patent-writing")
public class PatentWritingController {
    
    @Autowired
    private PatentWritingCoordinator coordinator;
    
    @Autowired
    private SseService sseService;
    
    /**
     * 创建专利写作任务
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createPatentWritingTask(
            @RequestBody PatentWritingRequest request) {
        
        String taskId = UUID.randomUUID().toString();
        
        // 启动专利写作任务
        coordinator.startPatentWriting(request, taskId);
        
        return ResponseEntity.ok(Map.of(
            "taskId", taskId,
            "status", "processing",
            "message", "专利写作任务已创建，请通过SSE获取进度"
        ));
    }
    
    /**
     * 获取任务状态
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<PatentWritingPlan> getTaskStatus(@PathVariable String taskId) {
        PatentWritingPlan plan = coordinator.getTaskStatus(taskId);
        return plan != null ? ResponseEntity.ok(plan) : ResponseEntity.notFound().build();
    }
    
    /**
     * SSE连接
     */
    @GetMapping("/stream/{taskId}")
    public SseEmitter streamTaskUpdates(@PathVariable String taskId) {
        return sseService.createConnection(taskId, "patent-writing");
    }
}
```

### 2. 专利写作协调器

```java
@Service
public class PatentWritingCoordinator {
    
    @Autowired
    private PatentPlanningService planningService;
    
    @Autowired
    private PatentStageExecutor stageExecutor;
    
    @Autowired
    private SseService sseService;
    
    private final Map<String, PatentWritingPlan> activePlans = new ConcurrentHashMap<>();
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    
    /**
     * 启动专利写作
     */
    public void startPatentWriting(PatentWritingRequest request, String taskId) {
        executorService.submit(() -> {
            try {
                // 1. 创建写作计划
                PatentWritingPlan plan = planningService.createWritingPlan(request, taskId);
                activePlans.put(taskId, plan);
                
                // 2. 通知前端
                sseService.sendTaskUpdate(taskId, plan);
                
                // 3. 开始执行阶段
                executeNextStage(taskId);
                
            } catch (Exception e) {
                logger.error("专利写作任务失败: {}", taskId, e);
                updatePlanStatus(taskId, "FAILED");
            }
        });
    }
    
    /**
     * 执行下一阶段
     */
    private void executeNextStage(String taskId) {
        PatentWritingPlan plan = activePlans.get(taskId);
        PatentWritingStage nextStage = findNextStage(plan);
        
        if (nextStage != null) {
            stageExecutor.executeStage(taskId, nextStage);
        } else {
            completePlan(taskId);
        }
    }
    
    /**
     * 阶段完成回调
     */
    public void onStageCompleted(String taskId, PatentWritingStage stage) {
        PatentWritingPlan plan = activePlans.get(taskId);
        
        // 更新计划状态
        updatePlanWithStageResult(plan, stage);
        
        // 通知前端
        sseService.sendTaskUpdate(taskId, plan);
        
        // 执行下一阶段
        executeNextStage(taskId);
    }
}
```

### 3. 阶段执行器

```java
@Service
public class PatentStageExecutor {
    
    @Autowired
    private PatentPromptBuilder promptBuilder;
    
    @Autowired
    private LlmService llmService;
    
    @Autowired
    private PatentWritingCoordinator coordinator;
    
    /**
     * 执行专利写作阶段
     */
    public void executeStage(String taskId, PatentWritingStage stage) {
        logger.info("执行专利写作阶段: {} - {}", taskId, stage.getStageName());
        
        try {
            // 更新阶段状态
            stage.setStatus("EXECUTING");
            stage.setStartTime(System.currentTimeMillis());
            
            // 根据阶段类型执行不同逻辑
            String result = switch (stage.getStageType()) {
                case "TOPIC_SELECTION" -> executeTopicSelection(taskId, stage);
                case "BACKGROUND_TECHNOLOGY" -> executeBackgroundAnalysis(taskId, stage);
                case "INVENTION_PURPOSE" -> executeInventionPurpose(taskId, stage);
                case "SOLUTION_OVERVIEW" -> executeSolutionOverview(taskId, stage);
                default -> throw new IllegalArgumentException("未知阶段类型: " + stage.getStageType());
            };
            
            // 更新阶段结果
            stage.setResult(result);
            stage.setStatus("COMPLETED");
            stage.setEndTime(System.currentTimeMillis());
            
            // 通知协调器阶段完成
            coordinator.onStageCompleted(taskId, stage);
            
        } catch (Exception e) {
            logger.error("阶段执行失败: {} - {}", taskId, stage.getStageName(), e);
            stage.setStatus("FAILED");
            stage.setResult("执行失败: " + e.getMessage());
            stage.setEndTime(System.currentTimeMillis());
        }
    }
    
    /**
     * 执行选题分析阶段
     */
    private String executeTopicSelection(String taskId, PatentWritingStage stage) {
        PatentWritingPlan plan = coordinator.getTaskStatus(taskId);
        
        // 构建选题分析提示词
        String prompt = promptBuilder.buildTopicSelectionPrompt(
            plan.getContext().getInnovationDescription()
        );
        
        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();
        
        // 解析结果并更新上下文
        updateContextWithTopicResult(plan.getContext(), result);
        
        return result;
    }
    
    /**
     * 执行背景技术阶段
     */
    private String executeBackgroundAnalysis(String taskId, PatentWritingStage stage) {
        PatentWritingPlan plan = coordinator.getTaskStatus(taskId);
        
        // 构建背景分析提示词
        String prompt = promptBuilder.buildBackgroundAnalysisPrompt(
            plan.getContext()
        );
        
        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();
        
        // 更新上下文
        updateContextWithBackgroundResult(plan.getContext(), result);
        
        return result;
    }
    
    /**
     * 执行发明目的阶段
     */
    private String executeInventionPurpose(String taskId, PatentWritingStage stage) {
        PatentWritingPlan plan = coordinator.getTaskStatus(taskId);
        
        // 构建发明目的提示词
        String prompt = promptBuilder.buildInventionPurposePrompt(
            plan.getContext()
        );
        
        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();
        
        // 更新上下文
        updateContextWithPurposeResult(plan.getContext(), result);
        
        return result;
    }
    
    /**
     * 执行方案概述阶段
     */
    private String executeSolutionOverview(String taskId, PatentWritingStage stage) {
        PatentWritingPlan plan = coordinator.getTaskStatus(taskId);
        
        // 构建方案概述提示词
        String prompt = promptBuilder.buildSolutionOverviewPrompt(
            plan.getContext()
        );
        
        // 调用AI执行
        ChatResponse response = llmService.getChatClient().call(new Prompt(prompt));
        String result = response.getResult().getOutput().getContent();
        
        // 更新上下文
        updateContextWithSolutionResult(plan.getContext(), result);
        
        return result;
    }
}
```

### 4. 专利提示词构建器

```java
@Component
public class PatentPromptBuilder {
    
    /**
     * 构建选题分析提示词
     */
    public String buildTopicSelectionPrompt(String innovationDescription) {
        return """
            # 专利选题分析专家
            
            ## 角色定位
            你是一位资深的专利代理人，擅长从技术创新中识别专利价值点。
            
            ## 任务目标
            对用户提供的技术创新进行专利选题分析，确定专利撰写方向。
            
            ## 分析要求
            1. **技术领域识别**
               - 确定技术所属的主要领域
               - 识别相关的技术分支
               - 分析技术的应用场景
            
            2. **创新点提取**
               - 识别核心技术创新点（至少2-3个）
               - 分析每个创新点的技术特征
               - 评估创新点的技术价值
            
            3. **专利性评估**
               - 初步判断新颖性
               - 分析创造性潜力
               - 评估实用性
            
            ## 输出格式
            请按以下格式输出：
            
            ### 技术领域
            [详细描述技术所属领域]
            
            ### 核心创新点
            1. **创新点1**: [创新点描述]
               - 技术特征: [具体技术特征]
               - 技术价值: [技术价值分析]
            
            2. **创新点2**: [创新点描述]
               - 技术特征: [具体技术特征]
               - 技术价值: [技术价值分析]
            
            ### 专利性分析
            - **新颖性**: [新颖性评估]
            - **创造性**: [创造性评估]
            - **实用性**: [实用性评估]
            
            ### 建议专利标题
            [建议的专利标题]
            
            ## 技术创新描述
            """ + innovationDescription;
    }
    
    /**
     * 构建背景技术分析提示词
     */
    public String buildBackgroundAnalysisPrompt(PatentWritingContext context) {
        return String.format("""
            # 专利背景技术分析专家
            
            ## 角色定位
            你是专利撰写专家，擅长技术背景分析和现有技术调研。
            
            ## 任务目标
            基于前期选题分析结果，深入分析技术背景，为专利撰写奠定基础。
            
            ## 已知信息
            - 技术领域: %s
            - 核心创新点: %s
            
            ## 分析要求
            1. **技术背景**
               - 描述技术发展历程
               - 分析当前技术水平
               - 说明技术发展趋势
            
            2. **现有技术**
               - 收集相关技术方案
               - 分析现有技术的优缺点
               - 识别技术空白和不足
            
            3. **技术问题**
               - 明确现有技术存在的问题
               - 分析问题的技术原因
               - 评估问题的影响程度
            
            ## 输出格式
            
            ### 技术背景
            [详细描述技术发展背景和现状，300-500字]
            
            ### 现有技术
            #### 技术方案一
            - **技术原理**: [技术原理描述]
            - **主要优点**: [优点列举]
            - **存在问题**: [问题分析]
            
            #### 技术方案二
            - **技术原理**: [技术原理描述]
            - **主要优点**: [优点列举]
            - **存在问题**: [问题分析]
            
            ### 技术问题总结
            1. **问题1**: [问题描述及影响]
            2. **问题2**: [问题描述及影响]
            3. **问题3**: [问题描述及影响]
            
            请确保分析客观、准确，为后续发明目的的阐述提供有力支撑。
            """, 
            context.getTechnicalField(),
            String.join(", ", context.getCoreInnovations())
        );
    }
    
    /**
     * 构建发明目的提示词
     */
    public String buildInventionPurposePrompt(PatentWritingContext context) {
        return String.format("""
            # 专利发明目的定义专家
            
            ## 角色定位
            你是专利撰写专家，擅长基于技术背景精确定义发明目的。
            
            ## 任务目标
            基于前期技术背景分析，明确定义本发明的目的和要解决的技术问题。
            
            ## 已知信息
            - 技术领域: %s
            - 核心创新点: %s
            - 技术背景: %s
            - 现有技术问题: %s
            
            ## 撰写要求
            1. **技术问题定义**
               - 问题表述准确、具体
               - 与现有技术的不足直接对应
               - 具有技术性和客观性
            
            2. **发明目的阐述**
               - 目的明确、针对性强
               - 与技术问题紧密关联
               - 体现技术进步性
            
            3. **解决方案概述**
               - 简要说明解决思路
               - 突出核心技术手段
               - 为后续详细方案铺垫
            
            ## 输出格式
            
            ### 要解决的技术问题
            现有技术存在以下技术问题：
            1. [技术问题1的具体描述]
            2. [技术问题2的具体描述]
            3. [技术问题3的具体描述]
            
            ### 发明目的
            本发明的目的是提供一种[技术方案名称]，以解决现有技术中存在的上述技术问题，实现[预期目标]。
            
            ### 解决方案概述
            为实现上述目的，本发明采用以下技术方案：
            [简要描述核心技术思路，100-200字]
            
            ### 有益效果
            采用本发明的技术方案，具有以下有益效果：
            1. [有益效果1]: [具体说明]
            2. [有益效果2]: [具体说明]
            3. [有益效果3]: [具体说明]
            
            请确保内容逻辑清晰，表述准确专业。
            """,
            context.getTechnicalField(),
            String.join(", ", context.getCoreInnovations()),
            context.getBackgroundAnalysis(),
            String.join(", ", context.getTechnicalProblems())
        );
    }
    
    /**
     * 构建方案概述提示词
     */
    public String buildSolutionOverviewPrompt(PatentWritingContext context) {
        return String.format("""
            # 专利技术方案描述专家
            
            ## 角色定位
            你是专利撰写专家，擅长围绕创新点构建完整的技术方案描述。
            
            ## 任务目标
            基于前期分析结果，围绕核心创新点详细描述技术方案。
            
            ## 已知信息
            - 技术领域: %s
            - 核心创新点: %s
            - 发明目的: %s
            - 要解决的技术问题: %s
            
            ## 撰写要求
            1. **围绕创新点展开**
               - 每个创新点都要详细阐述
               - 说明创新点的技术原理
               - 体现创新点的技术优势
            
            2. **技术方案完整性**
               - 整体架构清晰
               - 关键技术模块明确
               - 技术实现路径可行
            
            3. **实施方式具体**
               - 提供具体实施例
               - 包含关键参数范围
               - 考虑变形实施方式
            
            ## 输出格式
            
            ### 技术方案总体描述
            [从整体上描述技术方案的构成、工作原理和技术路线，200-300字]
            
            ### 核心创新点详述
            #### 创新点一：[创新点名称]
            - **技术特征**: [详细描述技术特征和实现方式]
            - **技术原理**: [阐述技术原理和作用机制]
            - **技术优势**: [说明相对于现有技术的优势]
            - **实现方式**: [描述具体实现方法]
            
            #### 创新点二：[创新点名称]
            - **技术特征**: [详细描述技术特征和实现方式]
            - **技术原理**: [阐述技术原理和作用机制]
            - **技术优势**: [说明相对于现有技术的优势]
            - **实现方式**: [描述具体实现方法]
            
            ### 具体实施例
            #### 实施例1
            - **应用场景**: [具体应用场景描述]
            - **技术参数**: [关键技术参数和数值范围]
            - **实施步骤**: [详细的实施步骤]
            - **预期效果**: [预期达到的技术效果]
            
            #### 实施例2（变形方式）
            - **变形特点**: [与实施例1的差异]
            - **适用场景**: [适用的特殊场景]
            - **技术调整**: [技术方案的调整内容]
            
            ### 权利要求要素提取
            #### 独立权利要求必要技术特征
            1. [必要技术特征1]
            2. [必要技术特征2]
            3. [必要技术特征3]
            
            #### 从属权利要求进一步限定
            1. [进一步限定特征1]
            2. [进一步限定特征2]
            3. [进一步限定特征3]
            
            请确保技术方案描述详细、准确，能够充分支撑权利要求的撰写。
            """,
            context.getTechnicalField(),
            String.join(", ", context.getCoreInnovations()),
            context.getInventionPurpose(),
            String.join(", ", context.getTechnicalProblems())
        );
    }
}
```

### 5. 数据模型

```java
@Data
public class PatentWritingPlan {
    private String taskId;
    private String patentTitle;
    private String currentStage;
    private String planStatus; // PLANNING, EXECUTING, COMPLETED, FAILED
    private List<PatentWritingStage> stages;
    private PatentWritingContext context;
    private long createdAt;
    private long updatedAt;
}

@Data
public class PatentWritingStage {
    private String stageId;
    private String stageName;
    private String stageType;
    private String stageDescription;
    private String status; // PENDING, EXECUTING, COMPLETED, FAILED
    private String result;
    private long startTime;
    private long endTime;
    private int order;
}

@Data
public class PatentWritingContext {
    private String innovationDescription;
    private String technicalField;
    private List<String> coreInnovations;
    private String backgroundAnalysis;
    private List<String> technicalProblems;
    private String inventionPurpose;
    private String solutionOverview;
    private Map<String, Object> additionalData;
}

@Data
public class PatentWritingRequest {
    private String innovationDescription;
    private String applicantInfo;
    private String inventorInfo;
    private String preferredField;
    private List<String> keywords;
}
```

## 🎯 核心特色

### 1. 严格的阶段化执行
- 按照专利写作的标准流程执行
- 每个阶段的输出作为下一阶段的输入
- 确保逻辑连贯性和专业性

### 2. 创新点驱动的写作
- 整个流程围绕技术创新点展开
- 每个阶段都强化创新点的描述
- 确保专利保护的核心价值

### 3. 专业化的AI提示词
- 针对专利写作的专业术语
- 符合专利法规要求的格式
- 渐进式的内容构建逻辑

### 4. 上下文累积机制
- 每个阶段的结果都累积到上下文中
- 后续阶段可以充分利用前期成果
- 保证内容的一致性和完整性

这个设计充分利用了现有架构的优势，同时针对专利写作的特殊需求进行了深度定制。
