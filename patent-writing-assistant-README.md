# 专利写作助手后端系统

## 🎯 系统概述

专利写作助手是基于Spring AI Alibaba Copilot架构开发的专业化AI写作系统，专门用于辅助专利文档的撰写。系统严格按照专利写作的标准流程，围绕技术创新点进行结构化的专利文档生成。

## 📋 核心特性

### 🔄 结构化写作流程
- **选题分析**：技术领域识别、创新点提取、专利性评估
- **背景技术**：技术背景调研、现有技术分析、技术问题识别
- **发明目的**：技术问题定义、解决方案概述、技术效果说明
- **方案概述**：创新点详述、技术方案描述、实施方式设计、权利要求构建

### 🎯 创新点驱动
- 整个写作过程围绕技术创新点展开
- 每个阶段都强化创新点的描述和分析
- 确保专利保护的核心价值得到充分体现

### 🤖 专业化AI提示词
- 针对专利写作的专业术语和格式要求
- 符合专利法规要求的规范化表述
- 渐进式的内容构建逻辑

### ⚡ 实时执行反馈
- 基于SSE的实时状态更新
- 分阶段的执行进度展示
- 详细的执行结果和错误信息

## 🏗️ 系统架构

```
专利写作助手后端
├── Controller层
│   └── PatentWritingController        # API接口控制器
├── Service层
│   ├── PatentWritingCoordinator       # 写作流程协调器
│   ├── PatentStageExecutor           # 阶段执行器
│   ├── PatentPlanningService         # 计划规划服务
│   └── PatentPromptBuilder           # 提示词构建器
├── Model层
│   ├── PatentWritingPlan             # 写作计划模型
│   ├── PatentWritingStage            # 写作阶段模型
│   ├── PatentWritingContext          # 写作上下文模型
│   └── PatentWritingRequest          # 写作请求模型
└── Configuration
    └── application-patent.properties  # 专利助手配置
```

## 🚀 快速开始

### 1. 环境要求
- Java 17+
- Maven 3.6+
- Spring Boot 3.4.5
- Spring AI 1.0.0

### 2. 配置AI模型
在 `application-patent.properties` 中配置AI模型：

```properties
# AI模型配置
spring.ai.openai.base-url=https://dashscope.aliyuncs.com/compatible-mode
spring.ai.openai.api-key=your-api-key-here
spring.ai.openai.chat.options.model=qwen-plus
```

### 3. 启动应用
```bash
mvn clean install
mvn spring-boot:run -Dspring.profiles.active=patent
```

### 4. 测试API
应用启动后，可以通过以下端点测试：

#### 健康检查
```bash
GET http://localhost:8080/api/patent-writing/health
```

#### 创建专利写作任务
```bash
POST http://localhost:8080/api/patent-writing/create
Content-Type: application/json

{
    "innovationDescription": "一种基于人工智能的智能推荐系统，通过深度学习算法分析用户行为模式，实现个性化内容推荐，提高推荐准确率和用户满意度。",
    "applicantInfo": "某某科技有限公司",
    "inventorInfo": "张三、李四",
    "preferredField": "人工智能",
    "keywords": ["人工智能", "推荐系统", "深度学习", "个性化推荐"]
}
```

#### 获取任务状态
```bash
GET http://localhost:8080/api/patent-writing/status/{taskId}
```

#### SSE实时更新
```bash
GET http://localhost:8080/api/patent-writing/stream/{taskId}
```

## 📝 API接口文档

### 1. 创建专利写作任务

**接口**: `POST /api/patent-writing/create`

**请求参数**:
```json
{
    "innovationDescription": "技术创新描述（必填）",
    "applicantInfo": "申请人信息（可选）",
    "inventorInfo": "发明人信息（可选）",
    "preferredField": "偏好技术领域（可选）",
    "keywords": ["关键词1", "关键词2"]
}
```

**响应示例**:
```json
{
    "taskId": "uuid-task-id",
    "status": "processing",
    "message": "专利写作任务已创建，请通过SSE连接获取实时进度",
    "timestamp": 1703123456789
}
```

### 2. 获取任务状态

**接口**: `GET /api/patent-writing/status/{taskId}`

**响应示例**:
```json
{
    "taskId": "uuid-task-id",
    "status": "EXECUTING",
    "patentTitle": "一种基于人工智能的智能推荐系统",
    "currentStage": "BACKGROUND_TECHNOLOGY",
    "stages": [
        {
            "stageId": "TOPIC_SELECTION",
            "stageName": "选题分析",
            "status": "COMPLETED",
            "result": "选题分析结果...",
            "startTime": 1703123456789,
            "endTime": 1703123466789
        }
    ],
    "createdAt": 1703123456789,
    "updatedAt": 1703123466789
}
```

### 3. SSE实时更新

**接口**: `GET /api/patent-writing/stream/{taskId}`

**事件类型**:
- `connected`: 连接建立
- `taskUpdate`: 任务状态更新
- `stageCompleted`: 阶段完成
- `error`: 错误信息

### 4. 取消任务

**接口**: `POST /api/patent-writing/cancel/{taskId}`

### 5. 重试失败阶段

**接口**: `POST /api/patent-writing/retry/{taskId}/{stageId}`

### 6. 获取活跃任务

**接口**: `GET /api/patent-writing/active`

## 🔧 配置说明

### 专利写作助手配置
```properties
# 最大创新描述长度
patent.writing.max-innovation-description-length=5000

# 最大并发任务数
patent.writing.max-concurrent-tasks=10

# 阶段执行超时时间（分钟）
patent.writing.stage-timeout-minutes=30

# 启用自动重试
patent.writing.enable-auto-retry=true

# 最大重试次数
patent.writing.max-retry-attempts=3
```

### 线程池配置
```properties
# 核心线程数
patent.writing.thread-pool.core-size=5

# 最大线程数
patent.writing.thread-pool.max-size=20

# 队列容量
patent.writing.thread-pool.queue-capacity=100

# 线程保活时间（秒）
patent.writing.thread-pool.keep-alive-seconds=60
```

## 📊 执行流程

### 1. 任务创建流程
```
用户提交请求 → 参数验证 → 创建写作计划 → 初始化上下文 → 启动第一阶段
```

### 2. 阶段执行流程
```
选题分析 → 背景技术 → 发明目的 → 方案概述 → 生成最终文档
```

### 3. 每个阶段的执行步骤
```
构建提示词 → 调用AI模型 → 解析结果 → 更新上下文 → 通知前端 → 进入下一阶段
```

## 🎯 专利写作输出示例

### 选题分析阶段输出
```
### 技术领域
本发明涉及人工智能技术领域，具体涉及一种基于深度学习的智能推荐系统...

### 核心创新点
1. **多维度用户行为分析**: 通过深度学习算法分析用户的多维度行为特征...
2. **动态推荐策略调整**: 基于实时反馈动态调整推荐策略...

### 专利性分析
- **新颖性评估**: 现有推荐系统主要基于协同过滤...
- **创造性评估**: 本发明通过多维度行为分析...
```

### 背景技术阶段输出
```
### 技术背景
随着互联网技术的快速发展，个性化推荐系统已成为各类应用平台的核心技术...

### 现有技术
#### 现有技术方案一
- **技术原理**: 协同过滤推荐算法...
- **存在问题**: 冷启动问题、数据稀疏性问题...
```

### 发明目的阶段输出
```
### 发明要解决的技术问题
现有技术存在以下需要解决的技术问题：
1. 推荐准确率不高，无法满足用户个性化需求
2. 冷启动问题严重，新用户推荐效果差...

### 发明目的
本发明的目的是提供一种基于人工智能的智能推荐系统...
```

### 方案概述阶段输出
```
### 技术方案总体描述
本发明提供的智能推荐系统主要包括用户行为分析模块、深度学习推荐模块...

### 核心创新点详述
#### 创新点一：多维度用户行为分析
- **技术特征**: 通过深度神经网络分析用户的浏览、点击、购买等多维度行为...
- **技术原理**: 采用注意力机制和循环神经网络...
```

## 🔍 监控和调试

### 日志配置
系统提供详细的日志记录，可以通过以下配置调整日志级别：

```properties
logging.level.com.alibaba.cloud.ai.example.patent=DEBUG
```

### 关键日志信息
- 任务创建和启动
- 阶段执行开始和结束
- AI模型调用和响应
- 错误和异常信息

### 性能监控
- 任务执行时间统计
- 阶段执行耗时分析
- 并发任务数量监控
- AI模型调用频率统计

## 🚨 错误处理

### 常见错误类型
1. **参数验证错误**: 创新描述为空或过长
2. **AI模型调用错误**: API密钥无效、网络超时
3. **阶段执行错误**: 提示词构建失败、结果解析错误
4. **系统资源错误**: 线程池满、内存不足

### 错误恢复机制
- 自动重试机制
- 阶段级别的错误隔离
- 详细的错误信息记录
- 用户友好的错误提示

## 📈 扩展和定制

### 1. 添加新的写作阶段
```java
// 在PatentPlanningService中添加新阶段
private PatentWritingStage createNewStage() {
    PatentWritingStage stage = new PatentWritingStage();
    stage.setStageId("NEW_STAGE");
    stage.setStageName("新阶段");
    // ... 其他配置
    return stage;
}
```

### 2. 自定义提示词
```java
// 在PatentPromptBuilder中添加新的提示词方法
public String buildCustomPrompt(PatentWritingContext context) {
    return "自定义提示词内容...";
}
```

### 3. 集成外部工具
```json
// 在mcp-servers-config.json中添加新工具
{
  "mcpServers": {
    "patent-search": {
      "command": "npx",
      "args": ["-y", "@patentai/mcp-server-patent-search"]
    }
  }
}
```

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 检查日志文件获取详细错误信息
2. 确认AI模型配置是否正确
3. 验证网络连接和API密钥
4. 查看系统资源使用情况

## 📄 许可证

本项目基于Apache 2.0许可证开源。
