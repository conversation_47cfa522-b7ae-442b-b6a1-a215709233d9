# 专利写作助手Agent设计方案

## 🎯 设计概述

基于Spring AI Alibaba Copilot的Agent架构，设计一个专业的专利写作助手，围绕创新点进行结构化的专利文档撰写。

## 📋 专利写作流程设计

### 核心写作阶段

```
专利写作流程 (Patent Writing Workflow)
├── 1. 选题阶段 (TOPIC_SELECTION)
│   ├── 1.1 技术领域识别
│   ├── 1.2 创新点提取
│   └── 1.3 专利性初步评估
├── 2. 背景分析阶段 (BACKGROUND_ANALYSIS)
│   ├── 2.1 技术背景调研
│   ├── 2.2 现有技术分析
│   ├── 2.3 技术问题识别
│   └── 2.4 相似专利检索
├── 3. 发明目的阶段 (INVENTION_PURPOSE)
│   ├── 3.1 技术问题定义
│   ├── 3.2 解决方案概述
│   └── 3.3 技术效果预期
└── 4. 方案概述阶段 (SOLUTION_OVERVIEW)
    ├── 4.1 创新点详细描述
    ├── 4.2 技术方案阐述
    ├── 4.3 实施方式规划
    └── 4.4 权利要求构建
```

## 🏗️ 系统架构设计

### 1. 核心组件架构

```java
// 专利写作任务规划器
@Service
public class PatentWritingPlanningService {
    
    // 基于创新点的任务规划
    public PatentWritingPlan createInnovationBasedPlan(String innovationDescription, String taskId);
    
    // 阶段性步骤生成
    public List<PatentWritingStep> generateStageSteps(PatentWritingStage stage, String context);
    
    // 动态步骤调整
    public PatentWritingStep planNextStep(String currentStageResult, PatentWritingContext context);
}

// 专利写作协调器
@Service
public class PatentWritingCoordinator {
    
    // 启动专利写作任务
    public void startPatentWriting(PatentWritingRequest request, String taskId);
    
    // 执行写作步骤
    public void executeWritingStep(String taskId, PatentWritingStep step);
    
    // 阶段完成处理
    public void completeStage(String taskId, PatentWritingStage stage);
}
```

### 2. 数据模型设计

```java
// 专利写作计划
@Data
public class PatentWritingPlan {
    private String taskId;
    private String patentTitle;
    private String innovationCore;           // 核心创新点
    private String technicalField;           // 技术领域
    private List<PatentWritingStage> stages; // 写作阶段
    private PatentWritingContext context;    // 上下文信息
    private String currentStage;             // 当前阶段
    private String planStatus;               // 计划状态
}

// 专利写作阶段
@Data
public class PatentWritingStage {
    private String stageId;
    private String stageName;                // 阶段名称
    private String stageType;                // 阶段类型
    private String stageDescription;         // 阶段描述
    private List<PatentWritingStep> steps;   // 步骤列表
    private String stageStatus;              // 阶段状态
    private String stageResult;              // 阶段结果
    private Map<String, Object> stageData;   // 阶段数据
}

// 专利写作步骤
@Data
public class PatentWritingStep {
    private int stepIndex;
    private String stepName;
    private String stepType;                 // 步骤类型
    private String stepRequirement;          // 步骤要求
    private String toolName;                 // 使用工具
    private String stepResult;               // 步骤结果
    private String status;                   // 执行状态
    private long startTime;
    private long endTime;
    private Map<String, Object> stepData;    // 步骤数据
}

// 专利写作上下文
@Data
public class PatentWritingContext {
    private String innovationDescription;    // 创新描述
    private String technicalField;           // 技术领域
    private String applicantInfo;            // 申请人信息
    private String inventorInfo;             // 发明人信息
    private List<String> keywords;           // 关键词
    private Map<String, String> priorArt;    // 现有技术
    private List<String> technicalProblems;  // 技术问题
    private List<String> technicalEffects;   // 技术效果
    private Map<String, Object> extraData;   // 扩展数据
}
```

## 🎯 专利写作提示词设计

### 1. 选题阶段提示词

```java
public class PatentTopicSelectionPromptBuilder {
    
    public String buildTopicAnalysisPrompt(String innovationDescription) {
        return """
            # 专利选题分析专家
            
            ## 角色定位
            你是一位资深的专利代理人和技术专家，擅长从技术创新中识别专利价值和撰写方向。
            
            ## 任务目标
            基于用户提供的技术创新描述，进行专业的专利选题分析，确定专利撰写的核心方向。
            
            ## 分析维度
            1. **技术领域识别**
               - 确定技术所属的IPC分类
               - 识别相关的技术子领域
               - 分析技术的交叉领域特征
            
            2. **创新点提取**
               - 识别核心技术创新点
               - 分析创新的技术层次
               - 评估创新的独创性程度
            
            3. **专利性初步评估**
               - 新颖性初步判断
               - 创造性潜力分析
               - 实用性评估
            
            ## 输出要求
            请以JSON格式输出分析结果：
            {
                "patentTitle": "建议的专利标题",
                "technicalField": "技术领域",
                "ipcClassification": "IPC分类号",
                "coreInnovations": ["核心创新点1", "核心创新点2"],
                "technicalLayers": ["技术层次1", "技术层次2"],
                "patentabilityAssessment": {
                    "novelty": "新颖性评估",
                    "inventiveness": "创造性评估",
                    "utility": "实用性评估"
                },
                "recommendedFocus": "建议的撰写重点",
                "nextSteps": ["下一步行动1", "下一步行动2"]
            }
            
            ## 用户输入的技术创新描述
            {innovationDescription}
            """;
    }
}
```

### 2. 背景分析阶段提示词

```java
public class PatentBackgroundAnalysisPromptBuilder {
    
    public String buildBackgroundResearchPrompt(String technicalField, List<String> coreInnovations) {
        return """
            # 专利背景分析专家
            
            ## 角色定位
            你是一位专业的专利检索和技术分析专家，擅长技术背景调研和现有技术分析。
            
            ## 任务目标
            针对指定的技术领域和创新点，进行全面的技术背景分析，为专利撰写提供坚实基础。
            
            ## 分析任务
            1. **技术背景调研**
               - 技术发展历程梳理
               - 当前技术水平分析
               - 技术发展趋势预测
            
            2. **现有技术分析**
               - 相关技术方案收集
               - 技术方案优缺点分析
               - 技术空白点识别
            
            3. **技术问题识别**
               - 现有技术存在的问题
               - 技术瓶颈分析
               - 用户需求痛点
            
            ## 输出格式
            请按以下结构输出分析结果：
            
            ### 技术背景
            [详细描述技术发展背景和现状]
            
            ### 现有技术
            1. **技术方案A**
               - 技术原理：
               - 优点：
               - 缺点：
               - 参考文献：
            
            2. **技术方案B**
               [同上格式]
            
            ### 技术问题
            1. **问题1**：[问题描述]
               - 影响：[问题影响]
               - 原因：[问题原因]
            
            2. **问题2**：[问题描述]
               [同上格式]
            
            ### 技术需求
            - [需求1]
            - [需求2]
            
            ## 分析对象
            - 技术领域：{technicalField}
            - 核心创新点：{coreInnovations}
            """;
    }
}
```

### 3. 发明目的阶段提示词

```java
public class PatentPurposeDefinitionPromptBuilder {
    
    public String buildPurposeDefinitionPrompt(String backgroundAnalysis, List<String> technicalProblems) {
        return """
            # 发明目的定义专家
            
            ## 角色定位
            你是一位专业的专利撰写专家，擅长基于技术背景和问题分析，精确定义发明目的。
            
            ## 任务目标
            基于前期的技术背景分析和问题识别，明确定义本发明的目的、要解决的技术问题和预期效果。
            
            ## 定义要求
            1. **技术问题定义**
               - 问题表述准确、具体
               - 问题具有技术性和客观性
               - 问题与现有技术的不足直接相关
            
            2. **解决方案概述**
               - 方案针对性强
               - 技术路径清晰
               - 创新点突出
            
            3. **技术效果预期**
               - 效果可量化或可验证
               - 效果与技术问题对应
               - 效果具有技术进步性
            
            ## 输出格式
            
            ### 本发明要解决的技术问题
            [准确、具体地描述要解决的技术问题，通常1-3个主要问题]
            
            ### 本发明的目的
            本发明的目的是提供一种[技术方案名称]，以解决上述技术问题。
            
            ### 解决方案概述
            为了实现上述目的，本发明采用以下技术方案：
            [简要描述技术方案的核心思路和关键技术]
            
            ### 有益效果
            采用上述技术方案，本发明具有以下有益效果：
            1. [效果1]：[具体描述]
            2. [效果2]：[具体描述]
            3. [效果3]：[具体描述]
            
            ## 输入信息
            - 技术背景分析：{backgroundAnalysis}
            - 识别的技术问题：{technicalProblems}
            """;
    }
}
```

### 4. 方案概述阶段提示词

```java
public class PatentSolutionOverviewPromptBuilder {
    
    public String buildSolutionDescriptionPrompt(String purposeDefinition, List<String> coreInnovations) {
        return """
            # 技术方案描述专家
            
            ## 角色定位
            你是一位资深的专利撰写专家，擅长围绕创新点构建完整的技术方案描述。
            
            ## 任务目标
            基于发明目的和核心创新点，详细描述技术方案，为后续的权利要求和说明书撰写奠定基础。
            
            ## 描述要求
            1. **创新点详细描述**
               - 每个创新点的技术原理
               - 创新点的技术特征
               - 创新点的技术优势
            
            2. **技术方案阐述**
               - 整体技术架构
               - 关键技术模块
               - 技术实现路径
            
            3. **实施方式规划**
               - 具体实施例设计
               - 变形实施方式
               - 参数范围确定
            
            ## 输出结构
            
            ### 技术方案总体描述
            [从整体上描述技术方案的构成和工作原理]
            
            ### 核心创新点详述
            #### 创新点1：[创新点名称]
            - **技术特征**：[详细描述技术特征]
            - **技术原理**：[阐述技术原理]
            - **技术优势**：[说明相对于现有技术的优势]
            - **实现方式**：[描述具体实现方式]
            
            #### 创新点2：[创新点名称]
            [同上格式]
            
            ### 技术方案架构
            1. **模块A**：[功能和实现]
            2. **模块B**：[功能和实现]
            3. **模块间关系**：[模块间的连接和交互]
            
            ### 具体实施例
            #### 实施例1
            - **应用场景**：[具体应用场景]
            - **技术参数**：[关键技术参数]
            - **实施步骤**：[详细实施步骤]
            - **预期效果**：[预期达到的效果]
            
            #### 实施例2
            [同上格式，展示技术方案的变形或扩展]
            
            ### 权利要求构建建议
            #### 独立权利要求要素
            - [必要技术特征1]
            - [必要技术特征2]
            - [必要技术特征3]
            
            #### 从属权利要求方向
            - [进一步限定方向1]
            - [进一步限定方向2]
            
            ## 输入信息
            - 发明目的定义：{purposeDefinition}
            - 核心创新点：{coreInnovations}
            """;
    }
}
```

## 🔧 专业化工具集成

### 1. 专利检索工具 (MCP集成)

```json
{
  "mcpServers": {
    "patent-search": {
      "command": "npx",
      "args": ["-y", "@patentai/mcp-server-patent-search"]
    },
    "patent-classification": {
      "command": "npx", 
      "args": ["-y", "@patentai/mcp-server-ipc-classification"]
    },
    "patent-analysis": {
      "command": "npx",
      "args": ["-y", "@patentai/mcp-server-patent-analysis"]
    }
  }
}
```

### 2. 专利格式化工具

```java
@Service
public class PatentFormattingService {
    
    // 专利文档格式化
    public String formatPatentDocument(PatentDocument document);
    
    // 权利要求格式化
    public String formatClaims(List<Claim> claims);
    
    // 说明书格式化
    public String formatSpecification(Specification spec);
    
    // IPC分类格式化
    public String formatIPCClassification(String classification);
}
```

## 🎯 执行流程设计

### 1. 任务启动流程

```java
@Service
public class PatentWritingCoordinator {
    
    public void startPatentWriting(PatentWritingRequest request, String taskId) {
        // 1. 创建写作计划
        PatentWritingPlan plan = planningService.createInnovationBasedPlan(
            request.getInnovationDescription(), taskId);
        
        // 2. 初始化上下文
        PatentWritingContext context = initializeContext(request);
        plan.setContext(context);
        
        // 3. 开始第一阶段：选题分析
        executeStage(taskId, PatentWritingStage.TOPIC_SELECTION);
    }
    
    private void executeStage(String taskId, String stageType) {
        PatentWritingPlan plan = getActivePlan(taskId);
        PatentWritingStage stage = plan.getStageByType(stageType);
        
        // 执行阶段中的每个步骤
        for (PatentWritingStep step : stage.getSteps()) {
            executeStep(taskId, step);
        }
    }
}
```

### 2. 步骤执行流程

```java
private void executeStep(String taskId, PatentWritingStep step) {
    // 1. 更新步骤状态
    step.setStatus("EXECUTING");
    step.setStartTime(System.currentTimeMillis());
    
    // 2. 构建提示词
    String prompt = promptBuilder.buildStepPrompt(step, getContext(taskId));
    
    // 3. 调用AI执行
    String result = llmService.executeWithPrompt(prompt);
    
    // 4. 处理结果
    step.setStepResult(result);
    step.setStatus("COMPLETED");
    step.setEndTime(System.currentTimeMillis());
    
    // 5. 更新上下文
    updateContext(taskId, step);
    
    // 6. 通知前端
    sseService.sendStepUpdate(taskId, step);
}
```

## 🎨 前端界面设计

### 1. 专利写作工作台

```vue
<template>
  <div class="patent-writing-workspace">
    <!-- 左侧：写作进度和导航 -->
    <div class="writing-progress-panel">
      <PatentWritingProgress :plan="currentPlan" />
      <PatentStageNavigation :stages="stages" @stage-click="switchStage" />
    </div>
    
    <!-- 中间：当前阶段内容 -->
    <div class="writing-content-panel">
      <PatentStageEditor 
        :stage="currentStage" 
        :context="writingContext"
        @content-update="handleContentUpdate" />
    </div>
    
    <!-- 右侧：AI助手和工具 -->
    <div class="ai-assistant-panel">
      <PatentAIAssistant :taskId="taskId" />
      <PatentToolbox :availableTools="tools" />
    </div>
  </div>
</template>
```

### 2. 阶段化编辑器

```vue
<template>
  <div class="patent-stage-editor">
    <!-- 阶段标题和描述 -->
    <div class="stage-header">
      <h2>{{ stage.stageName }}</h2>
      <p>{{ stage.stageDescription }}</p>
    </div>
    
    <!-- 阶段内容编辑区 -->
    <div class="stage-content">
      <!-- 选题阶段 -->
      <TopicSelectionEditor v-if="stage.stageType === 'TOPIC_SELECTION'" />
      
      <!-- 背景分析阶段 -->
      <BackgroundAnalysisEditor v-if="stage.stageType === 'BACKGROUND_ANALYSIS'" />
      
      <!-- 发明目的阶段 -->
      <PurposeDefinitionEditor v-if="stage.stageType === 'INVENTION_PURPOSE'" />
      
      <!-- 方案概述阶段 -->
      <SolutionOverviewEditor v-if="stage.stageType === 'SOLUTION_OVERVIEW'" />
    </div>
    
    <!-- 阶段操作按钮 -->
    <div class="stage-actions">
      <a-button @click="generateContent">AI生成内容</a-button>
      <a-button @click="optimizeContent">优化内容</a-button>
      <a-button @click="nextStage" type="primary">下一阶段</a-button>
    </div>
  </div>
</template>
```

## 🚀 技术特色

### 1. 创新点驱动的写作流程
- 以技术创新点为核心组织整个写作流程
- 每个阶段都围绕创新点展开分析和描述
- 确保专利文档的逻辑一致性和技术连贯性

### 2. 专业化的AI提示词体系
- 针对专利写作的专业术语和格式要求
- 分阶段的渐进式提示词设计
- 结合专利法律要求的规范性指导

### 3. 智能化的质量控制
- 实时的格式检查和规范性验证
- 基于专利数据库的新颖性初步筛查
- 权利要求的逻辑一致性检查

### 4. 协作化的写作环境
- 支持多人协作的专利撰写
- 版本控制和修改追踪
- 专家审核和反馈机制

## 📈 扩展方向

1. **多语言专利撰写**：支持中英文等多语言专利文档生成
2. **专利族管理**：支持同一发明的多国专利申请管理
3. **专利分析报告**：自动生成专利技术分析和竞争分析报告
4. **专利价值评估**：基于AI的专利价值和风险评估
5. **专利监控预警**：竞争对手专利动态监控和侵权风险预警

这个设计方案充分利用了现有的Agent架构优势，同时针对专利写作的专业性需求进行了深度定制，能够为用户提供专业、高效的专利撰写体验。
